%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: PBL_03
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 99419e35c7ef325479035394ca627b72, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Artificial lights
      Text:
        m_Untranslated: "1. Expand the GameObject <b>Spotlight_01a.</b>\n\n2. Select
          <b>Light_Spot_1600lm,</b> the light illuminating the wall, and inspect
          its properties.\n\nThe intensity of this lamp is <b>1600 Lumen</b> and
          the temperature is <b>3500 Kelvin.</b> This corresponds to the intensity
          of a powerful 100W halogen bulb, with a warm tint. \n\nThe intensity of
          the light bulbs is usually provided by the manufacturer, in Lumen or Candela.\n\nCreating
          realistic lighting is as simple as looking up bulb values online, or using
          the handy sliders and presets in the Light's inspector.\n\n\n<b>Expert
          tips</b>\n\n\u2022 When using Lumens with a Spot light, use the <b>Reflector</b>
          mode (enabled by default), in order to have a correct simulation of the
          light intensity based on the cone angle of the Spot light. If you cannot
          see this checkbox, \"Show Additional Properties\" via the 3-dots menu in
          the Emission foldout.\n\n\u2022 To optimize performance, keep the Emission
          and Shadow <b>Fade Distance</b> properties as low as possible, so that
          the light and its shadows disappear beyond a certain distance (in meters)."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 10
    m_Pivot: {x: 65.399185, y: 1.121933, z: 16.2816}
    m_Rotation: {x: 0.074304186, y: 0.24890557, z: -0.019160373, w: 0.9654848}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
