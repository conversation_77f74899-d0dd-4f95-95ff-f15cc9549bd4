%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4913237776817256236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1230075585302205852}
  - component: {fileID: 7880006503359350210}
  - component: {fileID: 4566621853601451998}
  - component: {fileID: 3769832281104418786}
  m_Layer: 0
  m_Name: Light_Point_Bulb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1230075585302205852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4913237776817256236}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.154, y: -1.863, z: 0.036}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &7880006503359350210
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4913237776817256236}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 2
  m_Shape: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 32
  m_Range: 0.35
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 8
  m_Lightmapping: 4
  m_LightShadowCasterMode: 2
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 2800
  m_UseColorTemperature: 1
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!114 &4566621853601451998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4913237776817256236}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a68c43fe1f2a47cfa234b5eeaa98012, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Intensity: 8
  m_EnableSpotReflector: 0
  m_LuxAtDistance: 1
  m_InnerSpotPercent: 0
  m_SpotIESCutoffPercent: 100
  m_LightDimmer: 1
  m_VolumetricDimmer: 1
  m_LightUnit: 4
  m_FadeDistance: 28
  m_VolumetricFadeDistance: 10000
  m_AffectDiffuse: 1
  m_AffectSpecular: 1
  m_NonLightmappedOnly: 0
  m_ShapeWidth: 0.5
  m_ShapeHeight: 0.5
  m_AspectRatio: 1
  m_ShapeRadius: 0.025
  m_SoftnessScale: 1
  m_UseCustomSpotLightShadowCone: 0
  m_CustomSpotLightShadowCone: 30
  m_MaxSmoothness: 0.99
  m_ApplyRangeAttenuation: 1
  m_DisplayAreaLightEmissiveMesh: 0
  m_AreaLightCookie: {fileID: 0}
  m_IESPoint: {fileID: 0}
  m_IESSpot: {fileID: 0}
  m_IncludeForRayTracing: 1
  m_AreaLightShadowCone: 120
  m_UseScreenSpaceShadows: 0
  m_InteractsWithSky: 1
  m_AngularDiameter: 0.5
  m_FlareSize: 2
  m_FlareTint: {r: 1, g: 1, b: 1, a: 1}
  m_FlareFalloff: 4
  m_SurfaceTexture: {fileID: 0}
  m_SurfaceTint: {r: 1, g: 1, b: 1, a: 1}
  m_Distance: 1.5e+11
  m_UseRayTracedShadows: 0
  m_NumRayTracingSamples: 4
  m_FilterTracedShadow: 1
  m_FilterSizeTraced: 16
  m_SunLightConeAngle: 0.5
  m_LightShadowRadius: 0.5
  m_SemiTransparentShadow: 0
  m_ColorShadow: 1
  m_DistanceBasedFiltering: 0
  m_EvsmExponent: 15
  m_EvsmLightLeakBias: 0
  m_EvsmVarianceBias: 0.00001
  m_EvsmBlurPasses: 0
  m_LightlayersMask: 8
  m_LinkShadowLayers: 1
  m_ShadowNearPlane: 0.1
  m_BlockerSampleCount: 24
  m_FilterSampleCount: 16
  m_MinFilterSize: 0.1
  m_KernelSize: 5
  m_LightAngle: 1
  m_MaxDepthBias: 0.001
  m_ShadowResolution:
    m_Override: 512
    m_UseOverride: 0
    m_Level: 2
  m_ShadowDimmer: 1
  m_VolumetricShadowDimmer: 1
  m_ShadowFadeDistance: 10000
  m_UseContactShadow:
    m_Override: 0
    m_UseOverride: 1
    m_Level: 0
  m_RayTracedContactShadow: 0
  m_ShadowTint: {r: 0, g: 0, b: 0, a: 1}
  m_PenumbraTint: 0
  m_NormalBias: 0.75
  m_SlopeBias: 0.5
  m_ShadowUpdateMode: 0
  m_AlwaysDrawDynamicShadows: 0
  m_UpdateShadowOnLightMovement: 0
  m_CachedShadowTranslationThreshold: 0.01
  m_CachedShadowAngularThreshold: 0.5
  m_BarnDoorAngle: 90
  m_BarnDoorLength: 0.05
  m_preserveCachedShadow: 0
  m_OnDemandShadowRenderOnPlacement: 1
  m_ShadowCascadeRatios:
  - 0.05
  - 0.2
  - 0.3
  m_ShadowCascadeBorders:
  - 0.2
  - 0.2
  - 0.2
  - 0.2
  m_ShadowAlgorithm: 0
  m_ShadowVariant: 0
  m_ShadowPrecision: 0
  useOldInspector: 0
  useVolumetric: 1
  featuresFoldout: 1
  m_AreaLightEmissiveMeshShadowCastingMode: 0
  m_AreaLightEmissiveMeshMotionVectorGenerationMode: 0
  m_AreaLightEmissiveMeshLayer: -1
  m_Version: 11
  m_ObsoleteShadowResolutionTier: 1
  m_ObsoleteUseShadowQualitySettings: 0
  m_ObsoleteCustomShadowResolution: 512
  m_ObsoleteContactShadows: 0
  m_PointlightHDType: 0
  m_SpotLightShape: 0
  m_AreaLightShape: 0
--- !u!114 &3769832281104418786
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4913237776817256236}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc4d76f733087744991913c9d19d5274, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LensFlareData: {fileID: 11400000, guid: 84cfb5afef7adac40b81ffb9d0dbed06, type: 2}
  intensity: 1
  maxAttenuationDistance: 10
  maxAttenuationScale: 10
  distanceAttenuationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  scaleByDistanceCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  attenuationByLightShape: 1
  radialScreenAttenuationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  useOcclusion: 1
  occlusionRadius: 0.05
  sampleCount: 32
  occlusionOffset: 0.05
  scale: 1
  allowOffScreen: 0
