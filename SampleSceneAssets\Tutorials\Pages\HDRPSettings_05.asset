%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: HDRPSettings_05
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: da7e71977534a894e967ad6546baec62, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Frame Settings
      Text:
        m_Untranslated: "You can control exactly which HDRP features the cameras
          and reflection probes can render, such as Rendering features, Post-Processing,
          Lighting, LODs, etc.\n\n1. Go to <b>Edit</b> > <b>Project Settings</b>
          > <b>Graphics</b> > <b>HDRP Global Settings.</b>\n\n2. Scroll down to the
          <b>Frame Settings (Default Values)</b> section.\n\n3. Under  <b>Camera</b>
          > <b>Rendering,</b> disable the <b>Decals.</b>\n\nNote that the water puddles
          and other dirt decals have disappears, now that the feature has been disabled
          for all cameras, by default.\n\n4. Re-enable the <b>Decals.</b>\n\n\n<b>Expert
          Tips</b>\n\nEach Camera and Reflection Probe component will let you override
          these default settings, via their <b>Custom Frame Settings</b> checkbox.\n\nFor
          instance, if a specific Reflection Probe in a Scene doesn't need to render
          Shadows Maps, Decals, or Volumetric Clouds, make sure you disable these
          features to maximize performance. \n\nYou may also tune the <b>Maximum
          LOD Level</b> to prevent Realtime Reflections from rendering unnecessarily
          complex objects, and recoup precious rendering time.\n\nMastering <b>Frame
          Settings</b> and per-object <b>Custom Frame Settings</b> is crucial to
          reach an optimal framerate."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 9.263008
    m_Pivot: {x: 4.573871, y: 5.9112844, z: 6.9082503}
    m_Rotation: {x: -0.034261636, y: 0.47704676, z: 0.018612023, w: 0.8780315}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
