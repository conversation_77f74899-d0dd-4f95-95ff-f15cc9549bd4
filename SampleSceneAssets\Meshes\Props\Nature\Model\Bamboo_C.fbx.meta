fileFormatVersion: 2
guid: d02feacdd25b360458c742d0b723ece0
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Bamboo_C 1
    100004: Geometry_Bamboo_C_LOD00
    100006: Geometry_Bamboo_C_LOD01
    100008: Geometry_Bamboo_C_LOD02
    400000: //RootNode
    400002: Bamboo_C 1
    400004: Geometry_Bamboo_C_LOD00
    400006: Geometry_Bamboo_C_LOD01
    400008: Geometry_Bamboo_C_LOD02
    2100000: Bamboo_A
    2100002: Bamboo_Leaf_A
    2300000: Bamboo_C 1
    2300002: Geometry_Bamboo_C_LOD00
    2300004: Geometry_Bamboo_C_LOD01
    2300006: Geometry_Bamboo_C_LOD02
    3300000: Bamboo_C 1
    3300002: Geometry_Bamboo_C_LOD00
    3300004: Geometry_Bamboo_C_LOD01
    3300006: Geometry_Bamboo_C_LOD02
    4300000: Bamboo_C
    4300002: Geometry_Bamboo_C_LOD00
    4300004: Geometry_Bamboo_C_LOD01
    4300006: Geometry_Bamboo_C_LOD02
    20500000: //RootNode
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Bamboo_A
    second: {fileID: 2100000, guid: fff7b0d103b7de54f810338cd8c6d44a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Bamboo_Leaf_A
    second: {fileID: 2100000, guid: 1ee01111bb691c348a61b9a5b38c8897, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
