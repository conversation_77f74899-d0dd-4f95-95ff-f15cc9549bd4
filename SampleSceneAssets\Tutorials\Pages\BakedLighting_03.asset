%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: BakedLighting_03
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 0afc96450840fdf46bc79303772d1b80, type: 3}
      m_Video: {fileID: 32900000, guid: 99f677dcf1744aa4d9ff3b0f37bf3888, type: 3}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Lightmapper Settings
      Text:
        m_Untranslated: "In the <b>Lighting</b> Window, you can find all the Lightmapper
          settings. These can be stored as <b>Lighting Settings</b> assets, at the
          very top of the window in the <b>Scene</b> tab.\r\n\r\nIn this template,
          5  <b>Lighting Settings</b> assets with different levels of quality are
          provided. Each level has a direct impact on how long the bake will take:
          the higher the quality, the longer the bake. \r\n\r\nMost of the provided
          Lighting Settings assets take advantage of the GPU Lightmapper, which offers
          much faster baking speed compared to the CPU Lightmapper, as long as you
          use a discrete GPU with sufficient processing power and video memory.\n\n\n<b>Important</b>\r\n\nTo
          correctly integrate the lighting from the Sky in a light bake, the Lightmapper
          requires a Volume Profile with at least one type of <b>Sky</b> override
          to be assigned inside the <b>Environment</b> tab, at the top of the <b>Lighting</b>
          Window, for every Scene. \n\nIf you do not specify a Volume Profile with
          a Sky override, the Lightmapper will consider the Sky is black. Therefore
          there might be a discrepancy between the resulting indirect lighting and
          the Sky rendered by the camera itself."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 5.4257765
    m_Pivot: {x: 44.59745, y: 2.8116028, z: 8.813792}
    m_Rotation: {x: -0.023293141, y: 0.49869338, z: 0.013454924, w: 0.86639863}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 0
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
