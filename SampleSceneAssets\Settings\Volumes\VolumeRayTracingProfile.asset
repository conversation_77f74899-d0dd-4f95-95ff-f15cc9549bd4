%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2847105570932170210
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d877a03bef431a847adca8ab343db3e1, type: 3}
  m_Name: RayTracingSettings
  m_EditorClassIdentifier: 
  active: 1
  rayBias:
    m_OverrideState: 0
    m_Value: 0.001
  extendShadowCulling:
    m_OverrideState: 0
    m_Value: 0
  extendCameraCulling:
    m_OverrideState: 0
    m_Value: 0
  directionalShadowRayLength:
    m_OverrideState: 0
    m_Value: 1000
  directionalShadowFallbackIntensity:
    m_OverrideState: 1
    m_Value: 0
  buildMode:
    m_OverrideState: 0
    m_Value: 0
  cullingMode:
    m_OverrideState: 0
    m_Value: 0
  cullingDistance:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: VolumeRayTracingProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 6426704641791874640}
  - {fileID: 9153205287893682077}
  - {fileID: 3641022199399448222}
  - {fileID: -2847105570932170210}
  - {fileID: 5442155027611909722}
  - {fileID: 2677739484256048676}
--- !u!114 &2677739484256048676
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 31394aa05878563408489d5c1688f3a0, type: 3}
  m_Name: PathTracing
  m_EditorClassIdentifier: 
  active: 0
  enable:
    m_OverrideState: 1
    m_Value: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: 4294967295
  maximumSamples:
    m_OverrideState: 0
    m_Value: 256
  minimumDepth:
    m_OverrideState: 0
    m_Value: 1
  maximumDepth:
    m_OverrideState: 0
    m_Value: 4
  maximumIntensity:
    m_OverrideState: 0
    m_Value: 10
  skyImportanceSampling:
    m_OverrideState: 0
    m_Value: 0
  tilingParameters:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 0, w: 0}
--- !u!114 &3641022199399448222
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: ScreenSpaceAmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  rayTracing:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 0
    m_Value: 2
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Bits: 819
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 1
    m_Value: 0
  m_StepCount:
    m_OverrideState: 1
    m_Value: 6
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 1
    m_Value: 40
  m_BilateralUpsample:
    m_OverrideState: 1
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 1
    m_Value: 2
  m_RayLength:
    m_OverrideState: 1
    m_Value: 1.3
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 4
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &5442155027611909722
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f1984a7ac01bf84b86559f7595cdc68, type: 3}
  m_Name: LightCluster
  m_EditorClassIdentifier: 
  active: 1
  cameraClusterRange:
    m_OverrideState: 1
    m_Value: 50
--- !u!114 &6426704641791874640
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 384c4d03a551c44448145f4093304119, type: 3}
  m_Name: ScreenSpaceReflection
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  enabled:
    m_OverrideState: 1
    m_Value: 1
  enabledTransparent:
    m_OverrideState: 0
    m_Value: 1
  tracing:
    m_OverrideState: 1
    m_Value: 4
  m_MinSmoothness:
    m_OverrideState: 1
    m_Value: 0.6
  m_SmoothnessFadeStart:
    m_OverrideState: 1
    m_Value: 0.8
  reflectSky:
    m_OverrideState: 0
    m_Value: 1
  usedAlgorithm:
    m_OverrideState: 0
    m_Value: 0
  depthBufferThickness:
    m_OverrideState: 0
    m_Value: 0.01
  screenFadeDistance:
    m_OverrideState: 0
    m_Value: 0.1
  accumulationFactor:
    m_OverrideState: 0
    m_Value: 0.75
  biasFactor:
    m_OverrideState: 0
    m_Value: 0.5
  speedRejectionParam:
    m_OverrideState: 0
    m_Value: 0.5
  speedRejectionScalerFactor:
    m_OverrideState: 0
    m_Value: 0.2
  speedSmoothReject:
    m_OverrideState: 0
    m_Value: 0
  speedSurfaceOnly:
    m_OverrideState: 0
    m_Value: 1
  speedTargetOnly:
    m_OverrideState: 0
    m_Value: 1
  enableWorldSpeedRejection:
    m_OverrideState: 0
    m_Value: 0
  m_RayMaxIterations:
    m_OverrideState: 0
    m_Value: 32
  rayMiss:
    m_OverrideState: 1
    m_Value: 1
  lastBounceFallbackHierarchy:
    m_OverrideState: 1
    m_Value: 2
  ambientProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: 4294967295
  textureLodBias:
    m_OverrideState: 0
    m_Value: 1
  m_RayLength:
    m_OverrideState: 1
    m_Value: 60
  m_ClampValue:
    m_OverrideState: 1
    m_Value: 10
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 1
    m_Value: 8
  m_AffectSmoothSurfaces:
    m_OverrideState: 0
    m_Value: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 1
  sampleCount:
    m_OverrideState: 0
    m_Value: 1
  bounceCount:
    m_OverrideState: 0
    m_Value: 1
  m_RayMaxIterationsRT:
    m_OverrideState: 1
    m_Value: 48
--- !u!114 &9153205287893682077
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 42ef2681fa3dc8c4fa031f044e68c63f, type: 3}
  m_Name: GlobalIllumination
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  enable:
    m_OverrideState: 1
    m_Value: 1
  tracing:
    m_OverrideState: 1
    m_Value: 4
  rayMiss:
    m_OverrideState: 1
    m_Value: 3
  depthBufferThickness:
    m_OverrideState: 0
    m_Value: 0.1
  fullResolutionSS:
    m_OverrideState: 0
    m_Value: 0
  m_MaxRaySteps:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiseSS:
    m_OverrideState: 1
    m_Value: 1
  m_HalfResolutionDenoiserSS:
    m_OverrideState: 1
    m_Value: 0
  m_DenoiserRadiusSS:
    m_OverrideState: 1
    m_Value: 0.6
  m_SecondDenoiserPassSS:
    m_OverrideState: 1
    m_Value: 1
  lastBounceFallbackHierarchy:
    m_OverrideState: 1
    m_Value: 2
  ambientProbeDimmer:
    m_OverrideState: 0
    m_Value: 0
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: 4294967295
  textureLodBias:
    m_OverrideState: 1
    m_Value: 1
  m_RayLength:
    m_OverrideState: 1
    m_Value: 20
  m_ClampValue:
    m_OverrideState: 1
    m_Value: 10
  mode:
    m_OverrideState: 1
    m_Value: 2
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  sampleCount:
    m_OverrideState: 1
    m_Value: 1
  bounceCount:
    m_OverrideState: 1
    m_Value: 3
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_HalfResolutionDenoiser:
    m_OverrideState: 0
    m_Value: 0
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.6
  m_SecondDenoiserPass:
    m_OverrideState: 0
    m_Value: 1
  m_MaxMixedRaySteps:
    m_OverrideState: 0
    m_Value: 48
  receiverMotionRejection:
    m_OverrideState: 1
    m_Value: 0
