fileFormatVersion: 2
guid: 3bb55b9dc42e34c47b35dea94575a162
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Rock_A_02_LOD00
    100004: Rock_A_02_LOD01
    100006: Rock_A_02_LOD02
    100008: Rock_A_02_LOD03
    100010: Rock_A_02_LOD04
    400000: //RootNode
    400002: Rock_A_02_LOD00
    400004: Rock_A_02_LOD01
    400006: Rock_A_02_LOD02
    400008: Rock_A_02_LOD03
    400010: Rock_A_02_LOD04
    2100000: Rock_A
    2300000: //RootNode
    2300002: Rock_A_02_LOD00
    2300004: Rock_A_02_LOD01
    2300006: Rock_A_02_LOD02
    2300008: Rock_A_02_LOD03
    2300010: Rock_A_02_LOD04
    3300000: //RootNode
    3300002: Rock_A_02_LOD00
    3300004: Rock_A_02_LOD01
    3300006: Rock_A_02_LOD02
    3300008: Rock_A_02_LOD03
    3300010: Rock_A_02_LOD04
    4300000: Rock_A_02
    4300002: Rock_A_02_LOD00
    4300004: Rock_A_02_LOD01
    4300006: Rock_A_02_LOD02
    4300008: Rock_A_02_LOD03
    4300010: Rock_A_02_LOD04
    20500000: //RootNode
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Rock_A
    second: {fileID: 2100000, guid: 5d53f2d3d2871cc40bfce7f567098cbe, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.0625
    - 0.03125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
