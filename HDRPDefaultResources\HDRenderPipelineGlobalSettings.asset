%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 781cc897cf8675041a751163b51f97dd, type: 3}
  m_Name: HDRenderPipelineGlobalSettings
  m_EditorClassIdentifier: 
  m_DefaultVolumeProfile: {fileID: 11400000, guid: 7f342610b85f4164f808a1f380dcc668, type: 2}
  m_LookDevVolumeProfile: {fileID: 11400000, guid: 254320a857a30444da2c99496a186368, type: 2}
  m_RenderingPathDefaultCameraFrameSettings:
    bitDatas:
      data1: 140666621263709
      data2: 4539628436200587288
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderingPathDefaultBakedOrCustomReflectionFrameSettings:
    bitDatas:
      data1: 139746950279965
      data2: 4539628424389492760
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderingPathDefaultRealtimeReflectionFrameSettings:
    bitDatas:
      data1: 139720912016133
      data2: 4539628424389492760
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderPipelineResources: {fileID: 11400000, guid: 3ce144cff5783da45aa5d4fdc2da14b7, type: 2}
  m_RenderPipelineRayTracingResources: {fileID: 0}
  beforeTransparentCustomPostProcesses: []
  beforePostProcessCustomPostProcesses: []
  afterPostProcessBlursCustomPostProcesses: []
  afterPostProcessCustomPostProcesses: []
  beforeTAACustomPostProcesses: []
  lightLayerName0: Light LayerDefault
  lightLayerName1: InteriorOnly
  lightLayerName2: ExteriorOnly
  lightLayerName3: LampsOnly
  lightLayerName4: ReflectionsOnly
  lightLayerName5: Light Layer 5
  lightLayerName6: Light Layer 6
  lightLayerName7: Light Layer 7
  decalLayerName0: Decal Layer default
  decalLayerName1: Decal Layer 1
  decalLayerName2: Decal Layer 2
  decalLayerName3: Decal Layer 3
  decalLayerName4: Decal Layer 4
  decalLayerName5: Decal Layer 5
  decalLayerName6: Decal Layer 6
  decalLayerName7: Decal Layer 7
  lensAttenuationMode: 0
  colorGradingSpace: 0
  m_ObsoleteDiffusionProfileSettingsList:
  - {fileID: 11400000, guid: 78322c7f82657514ebe48203160e3f39, type: 2}
  - {fileID: 11400000, guid: 26bdddf49760c61438938733f07fa2a2, type: 2}
  - {fileID: 11400000, guid: 5a9a2dc462c7bde4f86d0615a19c2c72, type: 2}
  - {fileID: 11400000, guid: 2b7005ba3a4d8474b8cdc34141ad766e, type: 2}
  rendererListCulling: 0
  DLSSProjectId: 000000
  useDLSSCustomProjectId: 0
  supportProbeVolumes: 0
  supportRuntimeDebugDisplay: 0
  autoRegisterDiffusionProfiles: 1
  apvScenesData:
    serializedBounds: []
    serializedHasVolumes: []
    serializedProfiles: []
    serializedBakeSettings: []
    serializedBakingSets: []
    m_LightingScenario: Default
  m_Version: 5
  m_ShaderVariantLogLevel: 0
  m_ExportShaderVariants: 1
