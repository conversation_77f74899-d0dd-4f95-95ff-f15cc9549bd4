%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: PBL_02
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 647cb5e6c7457754f80b7c8e36779962, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Sunlight
      Text:
        m_Untranslated: "The <b>Sunlight</b> in Unity is simulated via a directional
          light. In HDRP, we provide physically based units for its intensity and
          color. The unit of intensity for the sunlight is set in Lux, whereas the
          one for its color temperature is set in Kelvin.\n\nLet\u2019s see how the
          sun is set up in this scene.\n\n1. In the <i>Hierarchy,</i> select <b>Lighting</b>
          > <b>Directional Light Sun.</b>\n\n2. Notice in the <i>Inspector,</i> under
          <b>Emission</b>, that the <b>Intensity</b> is set to <b>100 000 lux.</b>
          It is a typical intensity for direct Sunlight on a clear day.\n\n3. Take
          a look at the <b>Temperature,</b> it is <b>5500 Kelvin.</b>\n\nThese two
          examples illustrate the Sun values commonly used for a realistic clear
          sky setup.\n\n\n<b>Expert Tips</b>\n\nSetting up the directional light's
          intensity is very important to ensure a correct lighting ratio in the scene,
          that is the difference of intensity between the lit and shaded areas of
          the scene.\n\nWhen using the Physically Based Sky, an intensity of <b>130
          000 lux</b> and a color temperature of <b>6500 kelvin</b> should be used
          at all time, as this type of sky will automatically dim and tint the sun
          light according to its position in the sky, defined by the rotation of
          the directional light."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 10
    m_Pivot: {x: -3.8249846, y: 6.47985, z: 4.302711}
    m_Rotation: {x: -0.0023974616, y: -0.45216167, z: -0.001220265, w: 0.89193237}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
