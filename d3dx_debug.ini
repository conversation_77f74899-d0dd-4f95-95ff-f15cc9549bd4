[ShaderOverride_Debug1]
; 替换1.hlsl为红色调试版本
hash = <1.hlsl的hash值>
ps = debug_red_ps

[ShaderOverride_Debug2]
; 替换2.hlsl为绿色调试版本  
hash = <2.hlsl的hash值>
ps = debug_green_ps

[ShaderOverride_Debug3]
; 替换3.hlsl为蓝色调试版本
hash = <3.hlsl的hash值>
ps = debug_blue_ps

[CustomShader_debug_red_ps]
; 简单的红色着色器
ps_5_0
dcl_globalFlags refactoringAllowed
dcl_input_ps linear v1.xy
dcl_output o0.xyzw
mov o0.xyz, l(1.000000, 0.000000, 0.000000, 0.000000)
mov o0.w, l(1.000000)
ret

[CustomShader_debug_green_ps]
; 简单的绿色着色器
ps_5_0
dcl_globalFlags refactoringAllowed
dcl_input_ps linear v1.xy
dcl_output o0.xyzw
mov o0.xyz, l(0.000000, 1.000000, 0.000000, 0.000000)
mov o0.w, l(1.000000)
ret

[CustomShader_debug_blue_ps]
; 简单的蓝色着色器
ps_5_0
dcl_globalFlags refactoringAllowed
dcl_input_ps linear v1.xy
dcl_output o0.xyzw
mov o0.xyz, l(0.000000, 0.000000, 1.000000, 0.000000)
mov o0.w, l(1.000000)
ret
