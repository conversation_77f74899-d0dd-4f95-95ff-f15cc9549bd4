%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: LightProbes_02
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: dba7af2c858b3e8419de858d1f56abb5, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Explore the Light Probes
      Text:
        m_Untranslated: "To visualize the probes, make sure the Gizmos are enabled
          in the Scene view, and at the bottom of the <b>Window</b> > <b>Rendering</b>
          > <b>Lighting</b> Window, set <b>Light Probe Visualization</b> to <b>All
          Probes No Cells.</b>\n\nThe <b>Light Probe Group</b> can be found in the
          <i>Hierarchy</i> <b>Lighting</b> > <b>Light Probe Group.</b> These Light
          Probes were placed by hand. \n\nSelect the Light Probe Group, then in the
          <i>Inspector,</i> click the <b>Edit Light Probes</b> button to select,
          move or duplicate probes.\n\nLight Probes are baked manually in the <b>Lighting</b>
          Window, via the <b>Generate Lighting</b> button.\n\n\n<b>Expert Tips</b>\n\n\u2022
          Light Probe Groups are not real-time, so remember to generate the lighting
          again if you made noticeable changes to the scene.\n\n\u2022 The spheres
          in the image above are a visualisation of the lighting captured by the
          Light Probes at their corresponding locations. Objects are set by default
          to receive lighting from Light Probes, and will receive the lighting as
          a blend from the 4 closest probes. Therefore, it\u2019s important to increase
          the density of probes in areas of high lighting contrast to ensure more
          accurate interpolation between probes.\n\n\u2022 It is recommended to set
          smaller stationary Mesh Renderers to receive Global Illumination from Light
          Probes, rather than from Lightmaps. Light Probes are baked considerably
          faster than lightmaps, and Light Probe data is considerably smaller than
          that of lightmaps. Apart from the main structures (walls, floors, etc.),
          most objects in this sample Scene are tagged to receive Global Illumination
          from Light Probes. This will reduce baking times and the overall memory
          footprint of the lighting."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 0.8660253
    m_Pivot: {x: 29.906303, y: 1.9557085, z: 6.273357}
    m_Rotation: {x: -0.07154695, y: -0.90737444, z: 0.17223871, w: -0.37680447}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
