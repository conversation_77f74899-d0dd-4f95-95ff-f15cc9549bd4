%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: Sky_01
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 8d2b462398d6d8d43be9ae973c781087, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Set up the Sky
      Text:
        m_Untranslated: "In HDRP, the following Volume overrides are required to
          set up the <b>Sky:</b>\r\n\r\n\u2022 A <b>Visual Environment</b> override
          which defines the type of sky to render, such as <b>HDRI Sky,</b> <b>Physically
          Base Sky</b> or <b>Gradient Sky.</b>\n\n\u2022 A dedicated Sky override
          that matches the type of Sky set in the <b>Visual Environment.</b>\n\n\n<b>Important</b>\r\n\nIn
          typical scenarios, the objects in the Scene rely on nearby Light Probes
          and Lightmaps for their indirect lighting. However, in case these two do
          not exist, HDRP will fall back on the Global Ambient Probe based of the
          Sky.\n\nFor this reason, in the <b>Visual Environment</b> override, it
          is important to select the <b>Ambient Mode</b> to control how the Global
          Ambient Probe's lighting is generated. Two modes are available:\n\n1. <b>Dynamic:</b>
          Its ambient lighting is automatically refreshed based on the currently
          rendered Sky. \n\nThis mode is very straightforward as it will ensure the
          ambient lighting in the scene always matches the rendered Sky seen by the
          camera.\n\n2. <b>Static:</b> Its ambient lighting will be generated based
          on the <b>Volume Profile</b> and the <b>Static Lighting Sky</b> assigned
          in the <b>Window</b> > <b>Rendering</b> > <b>Lighting</b> > <b>Environment</b>
          tab. \n\nThis mode is useful for Scenes whose Sky never vary, or in situations
          when the indirect lighting from the Global Ambient Probe needs to be generated
          from another Sky than the one rendered by the camera itself."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 10
    m_Pivot: {x: 0.4612465, y: 9.553326, z: 0.66909266}
    m_Rotation: {x: -0.11598007, y: 0.5433982, z: 0.07614133, w: 0.8279308}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
