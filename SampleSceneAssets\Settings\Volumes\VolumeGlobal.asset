%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7987056865732881293
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56b145d2b9ee1ac4f846968484e7485a, type: 3}
  m_Name: ContactShadows
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  enable:
    m_OverrideState: 1
    m_Value: 1
  length:
    m_OverrideState: 1
    m_Value: 0.05
  opacity:
    m_OverrideState: 0
    m_Value: 1
  distanceScaleFactor:
    m_OverrideState: 1
    m_Value: 0.5
  maxDistance:
    m_OverrideState: 0
    m_Value: 50
  minDistance:
    m_OverrideState: 1
    m_Value: 0
  fadeDistance:
    m_OverrideState: 0
    m_Value: 5
  fadeInDistance:
    m_OverrideState: 1
    m_Value: 0
  rayBias:
    m_OverrideState: 1
    m_Value: 1
  thicknessScale:
    m_OverrideState: 1
    m_Value: 0.15
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 24
--- !u!114 &-7045460808225037193
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 2
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.6
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.1
  radius:
    m_OverrideState: 1
    m_Value: 0.5
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 1
    m_Value: 6
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 1
    m_Value: 40
  m_BilateralUpsample:
    m_OverrideState: 1
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 1
    m_Value: 2
  m_RayLength:
    m_OverrideState: 1
    m_Value: 2
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 8
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 1
    m_Value: 0.65
--- !u!114 &-3969488579639410846
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bcf384b154398e341b6b29969c078198, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 2
  intensity:
    m_OverrideState: 1
    m_Value: 1
  maximumVelocity:
    m_OverrideState: 0
    m_Value: 200
  minimumVelocity:
    m_OverrideState: 0
    m_Value: 2
  cameraMotionBlur:
    m_OverrideState: 0
    m_Value: 1
  specialCameraClampMode:
    m_OverrideState: 0
    m_Value: 0
  cameraVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.05
  cameraTranslationVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.05
  cameraRotationVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.03
  depthComparisonExtent:
    m_OverrideState: 0
    m_Value: 1
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 12
--- !u!114 &-3315835157980468525
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a7ff42a8c5be6646ad3975f3a54c1eb, type: 3}
  m_Name: DiffusionProfileList
  m_EditorClassIdentifier: 
  active: 1
  diffusionProfiles:
    m_OverrideState: 1
    m_Value:
    - {fileID: 11400000, guid: 2384dbf2c1c420f45a792fbc315fbfb1, type: 2}
    - {fileID: 11400000, guid: 404820c4cf36ad944862fa59c56064f0, type: 2}
    - {fileID: 11400000, guid: 5a9a2dc462c7bde4f86d0615a19c2c72, type: 2}
--- !u!114 &-2223948172287066284
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 2
  threshold:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.2
  scatter:
    m_OverrideState: 1
    m_Value: 0.4
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 1
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-1167143466653396744
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59b6606ef2548734bb6d11b9d160bc7e, type: 3}
  m_Name: HDRISky
  m_EditorClassIdentifier: 
  active: 1
  rotation:
    m_OverrideState: 1
    m_Value: 116
  skyIntensityMode:
    m_OverrideState: 1
    m_Value: 0
  exposure:
    m_OverrideState: 1
    m_Value: 13.5
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 1
    m_Value: 0.4660715
  upperHemisphereLuxColor:
    m_OverrideState: 1
    m_Value: {x: 0.18750614, y: 0.29181972, z: 0.5}
  desiredLuxValue:
    m_OverrideState: 1
    m_Value: 25000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  hdriSky:
    m_OverrideState: 1
    m_Value: {fileID: 8900000, guid: 54a3a0570aebe8949bec4966f1376581, type: 3}
  distortionMode:
    m_OverrideState: 0
    m_Value: 0
  flowmap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  upperHemisphereOnly:
    m_OverrideState: 0
    m_Value: 1
  scrollOrientation:
    m_OverrideState: 1
    m_Value:
      mode: 0
      customValue: 116
      additiveValue: 0
      multiplyValue: 0
  scrollSpeed:
    m_OverrideState: 0
    m_Value:
      mode: 1
      customValue: 100
      additiveValue: 0
      multiplyValue: 1
  enableBackplate:
    m_OverrideState: 0
    m_Value: 0
  backplateType:
    m_OverrideState: 0
    m_Value: 0
  groundLevel:
    m_OverrideState: 0
    m_Value: 0
  scale:
    m_OverrideState: 0
    m_Value: {x: 32, y: 32}
  projectionDistance:
    m_OverrideState: 0
    m_Value: 16
  plateRotation:
    m_OverrideState: 0
    m_Value: 0
  plateTexRotation:
    m_OverrideState: 0
    m_Value: 0
  plateTexOffset:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0}
  blendAmount:
    m_OverrideState: 0
    m_Value: 0
  shadowTint:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  pointLightShadow:
    m_OverrideState: 0
    m_Value: 0
  dirLightShadow:
    m_OverrideState: 0
    m_Value: 0
  rectLightShadow:
    m_OverrideState: 0
    m_Value: 0
  m_SkyVersion: 1
  enableDistortion:
    m_OverrideState: 0
    m_Value: 0
  procedural:
    m_OverrideState: 0
    m_Value: 1
  scrollDirection:
    m_OverrideState: 0
    m_Value: 0
  m_ObsoleteScrollSpeed:
    m_OverrideState: 0
    m_Value: 2
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: VolumeGlobal
  m_EditorClassIdentifier: 
  components:
  - {fileID: 1359070828411649799}
  - {fileID: -1167143466653396744}
  - {fileID: 8049981929359091865}
  - {fileID: 6640133647794636954}
  - {fileID: 2759069934644561995}
  - {fileID: 8300247594604814326}
  - {fileID: -7987056865732881293}
  - {fileID: -7045460808225037193}
  - {fileID: -2223948172287066284}
  - {fileID: -3969488579639410846}
  - {fileID: 747098994415714109}
  - {fileID: 4566301752631259820}
  - {fileID: 6726343302422785154}
  - {fileID: -3315835157980468525}
--- !u!114 &747098994415714109
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a6b00fcf518bb94a90b408492e07b44, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  type:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.2
  response:
    m_OverrideState: 1
    m_Value: 0.8
  texture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
--- !u!114 &1359070828411649799
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  active: 1
  skyType:
    m_OverrideState: 1
    m_Value: 1
  cloudType:
    m_OverrideState: 1
    m_Value: 0
  skyAmbientMode:
    m_OverrideState: 1
    m_Value: 1
  windOrientation:
    m_OverrideState: 1
    m_Value: 0
  windSpeed:
    m_OverrideState: 1
    m_Value: 0
  fogType:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2759069934644561995
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 0
    m_Value: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
  baseHeight:
    m_OverrideState: 0
    m_Value: 0
  maximumHeight:
    m_OverrideState: 1
    m_Value: 500
  meanFreePath:
    m_OverrideState: 1
    m_Value: 5000
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  globalLightProbeDimmer:
    m_OverrideState: 1
    m_Value: 0
  depthExtent:
    m_OverrideState: 1
    m_Value: 50
  denoisingMode:
    m_OverrideState: 1
    m_Value: 3
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
  sliceDistributionUniformity:
    m_OverrideState: 1
    m_Value: 1
  m_FogControlMode:
    m_OverrideState: 1
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 1
    m_Value: 12.5
  volumeSliceCount:
    m_OverrideState: 1
    m_Value: 64
  m_VolumetricFogBudget:
    m_OverrideState: 1
    m_Value: 0.333
  m_ResolutionDepthRatio:
    m_OverrideState: 1
    m_Value: 0.5
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &4566301752631259820
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e17fad69ea181b4483974138b566975, type: 3}
  m_Name: ScreenSpaceRefraction
  m_EditorClassIdentifier: 
  active: 1
  screenFadeDistance:
    m_OverrideState: 1
    m_Value: 0.001
--- !u!114 &6640133647794636954
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 4
  meteringMode:
    m_OverrideState: 1
    m_Value: 4
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 12
  compensation:
    m_OverrideState: 1
    m_Value: 0
  limitMin:
    m_OverrideState: 1
    m_Value: 5
  limitMax:
    m_OverrideState: 1
    m_Value: 13
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 1
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 1
    m_Value: 4
  adaptationSpeedLightToDark:
    m_OverrideState: 1
    m_Value: 4
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 1
    m_Value: {x: 10, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &6726343302422785154
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7e9e4ed5a6f56fb4ebd693e39684f36f, type: 3}
  m_Name: VolumetricClouds
  m_EditorClassIdentifier: 
  active: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  localClouds:
    m_OverrideState: 1
    m_Value: 0
  earthCurvature:
    m_OverrideState: 0
    m_Value: 0
  cloudTiling:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1}
  cloudOffset:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0}
  bottomAltitude:
    m_OverrideState: 1
    m_Value: 1200
  altitudeRange:
    m_OverrideState: 1
    m_Value: 2000
  fadeInMode:
    m_OverrideState: 0
    m_Value: 0
  fadeInStart:
    m_OverrideState: 0
    m_Value: 0
  fadeInDistance:
    m_OverrideState: 0
    m_Value: 0
  numPrimarySteps:
    m_OverrideState: 0
    m_Value: 48
  numLightSteps:
    m_OverrideState: 0
    m_Value: 8
  cloudMap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  cloudLut:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  cloudControl:
    m_OverrideState: 0
    m_Value: 0
  cloudSimpleMode:
    m_OverrideState: 0
    m_Value: 0
  m_CloudPreset:
    m_OverrideState: 1
    m_Value: 1
  cumulusMap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  cumulusMapMultiplier:
    m_OverrideState: 0
    m_Value: 1
  altoStratusMap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  altoStratusMapMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cumulonimbusMap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  cumulonimbusMapMultiplier:
    m_OverrideState: 0
    m_Value: 1
  rainMap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 2
  cloudMapResolution:
    m_OverrideState: 0
    m_Value: 64
  densityCurve:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.15
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  erosionCurve:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.1
        value: 0.9
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  ambientOcclusionCurve:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.25
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  scatteringTint:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  powderEffectIntensity:
    m_OverrideState: 0
    m_Value: 0.7
  multiScattering:
    m_OverrideState: 0
    m_Value: 0.5
  densityMultiplier:
    m_OverrideState: 1
    m_Value: 0.4
  shapeFactor:
    m_OverrideState: 1
    m_Value: 0.9
  shapeScale:
    m_OverrideState: 1
    m_Value: 5
  shapeOffset:
    m_OverrideState: 1
    m_Value: {x: -0.25, y: 0.15, z: 0.25}
  erosionFactor:
    m_OverrideState: 1
    m_Value: 0.8
  erosionScale:
    m_OverrideState: 1
    m_Value: 107
  erosionNoiseType:
    m_OverrideState: 1
    m_Value: 1
  microErosion:
    m_OverrideState: 0
    m_Value: 0
  microErosionFactor:
    m_OverrideState: 1
    m_Value: 0.5
  microErosionScale:
    m_OverrideState: 1
    m_Value: 200
  ambientLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
  sunLightDimmer:
    m_OverrideState: 0
    m_Value: 1
  erosionOcclusion:
    m_OverrideState: 0
    m_Value: 0.1
  globalWindSpeed:
    m_OverrideState: 1
    m_Value:
      mode: 0
      customValue: 0
      additiveValue: 0
      multiplyValue: 0
  orientation:
    m_OverrideState: 0
    m_Value:
      mode: 0
      customValue: 0
      additiveValue: 0
      multiplyValue: 0
  altitudeDistortion:
    m_OverrideState: 0
    m_Value: 0.5
  cloudMapSpeedMultiplier:
    m_OverrideState: 0
    m_Value: 0.5
  shapeSpeedMultiplier:
    m_OverrideState: 0
    m_Value: 1
  erosionSpeedMultiplier:
    m_OverrideState: 0
    m_Value: 0.25
  verticalShapeWindSpeed:
    m_OverrideState: 1
    m_Value: 0
  verticalErosionWindSpeed:
    m_OverrideState: 1
    m_Value: 0
  temporalAccumulationFactor:
    m_OverrideState: 1
    m_Value: 0.602
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0
  perceptualBlending:
    m_OverrideState: 1
    m_Value: 0
  shadows:
    m_OverrideState: 0
    m_Value: 0
  shadowResolution:
    m_OverrideState: 0
    m_Value: 256
  shadowPlaneHeightOffset:
    m_OverrideState: 0
    m_Value: 0
  shadowDistance:
    m_OverrideState: 0
    m_Value: 8000
  shadowOpacity:
    m_OverrideState: 0
    m_Value: 1
  shadowOpacityFallback:
    m_OverrideState: 0
    m_Value: 0
  m_Version: 2
  m_ObsoleteWindSpeed:
    m_OverrideState: 1
    m_Value: 0
  m_ObsoleteOrientation:
    m_OverrideState: 0
    m_Value: 0
  m_ObsoleteShapeOffsetX:
    m_OverrideState: 0
    m_Value: 0
  m_ObsoleteShapeOffsetY:
    m_OverrideState: 0
    m_Value: 0
  m_ObsoleteShapeOffsetZ:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &8049981929359091865
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  useFullACES:
    m_OverrideState: 0
    m_Value: 0
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
  gamma:
    m_OverrideState: 0
    m_Value: 1
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  fallbackMode:
    m_OverrideState: 0
    m_Value: 1
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &8300247594604814326
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 70
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cascadeShadowSplitCount:
    m_OverrideState: 0
    m_Value: 4
  cascadeShadowSplit0:
    m_OverrideState: 1
    m_Value: 0.028571429
  cascadeShadowSplit1:
    m_OverrideState: 1
    m_Value: 0.057142857
  cascadeShadowSplit2:
    m_OverrideState: 1
    m_Value: 0.22857143
  cascadeShadowBorder0:
    m_OverrideState: 1
    m_Value: 0.25
  cascadeShadowBorder1:
    m_OverrideState: 1
    m_Value: 0.5
  cascadeShadowBorder2:
    m_OverrideState: 1
    m_Value: 0.375
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
