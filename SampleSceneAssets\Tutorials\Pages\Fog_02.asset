%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: Fog_02
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: bbac427ff2a44574ea772e6c16a5dce4, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Fog parameters
      Text:
        m_Untranslated: "Let's have a look at the Fog properties.\n\n1. In the <i>Hierarchy,</i>
          under <b>Lighting</b> > <b>Volumes,</b>  select <b>Volumes Global.</b>\n\n2.
          In its <i>Inspector,</i> locate the <b>Volume</b> component and expand
          the <b>Fog</b> override.\n\nThe following properties are some of the most
          important ones to set up the <b>Fog</b> correctly.\n\n<b>Fog Attenuation
          Distance:</b> Determines how far you can see through the Fog in meters.
          Lower values will produce denser levels of Fog. \n\n<b>Ambient Light Probe
          Dimmer:</b> Reduces the influence of the global ambient probe on the Fog.
          It is useful for darker indoor Scenes whose Fog should not be affected
          by the sky.\n\n<b>Volumetric Fog Distance:</b> Determines how far from
          the camera the volumetric fog will be rendered. \n\n<b>Denoising Mode:</b>\n\u2022
          <b>Gaussian</b> applies a fast blur to smooth out the Fog, but this may
          lead to a loss of sharper details.\n\u2022 <b>Reprojection</b> uses previous
          frames to increase the quality and precision of the Fog, but it may increase
          ghosting when the camera moves at higher speed.\n\u2022 <b>Both</b> can
          produce very smooth Fog using both techniques above. As a result, it is
          the most expensive method, while producing the best results.\n\n<b>Slice
          Distribution Uniformity:</b> The lower the value, the more precise the
          nearby Fog is, at the expense of the distant Fog. A value of 1 gives an
          equal distribution of quality between the nearby and distant Fog.\n\n<b>Directional
          Lights Only:</b> Improves performance by discarding local lights. Use this
          option if you only require volumetric effects from the main directional
          light."
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 0.99356216
    m_Pivot: {x: 26.617464, y: 2.1934526, z: 0.413616}
    m_Rotation: {x: 0.011757434, y: -0.7532886, z: -0.013500657, w: -0.6575062}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
