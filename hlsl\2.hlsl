// ---- Created with 3Dmigoto v1.2.52 on Sun Aug 10 16:07:52 2025
Texture2D<float4> t16 : register(t16);

Texture2D<float4> t15 : register(t15);

Texture2D<float4> t14 : register(t14);

Texture3D<float4> t13 : register(t13);

Texture3D<float4> t12 : register(t12);

Texture3D<float4> t11 : register(t11);

Texture3D<float4> t10 : register(t10);

Texture2D<float4> t9 : register(t9);

Texture2D<float4> t8 : register(t8);

Texture2D<float4> t7 : register(t7);

Texture2D<float4> t6 : register(t6);

Texture2D<float4> t5 : register(t5);

TextureCubeArray<float4> t3 : register(t3);

Texture2D<float4> t2 : register(t2);

Texture2D<float4> t1 : register(t1);

Texture2D<float4> t0 : register(t0);

SamplerState s4_s : register(s4);

SamplerComparisonState s3_s : register(s3);

SamplerState s2_s : register(s2);

SamplerState s1_s : register(s1);

SamplerState s0_s : register(s0);

cbuffer cb4 : register(b4)
{
  float4 cb4[369];
}

cbuffer cb3 : register(b3)
{
  float4 cb3[2054];
}

cbuffer cb2 : register(b2)
{
  float4 cb2[3];
}

cbuffer cb1 : register(b1)
{
  float4 cb1[196];
}

cbuffer cb0 : register(b0)
{
  float4 cb0[181];
}




// 3Dmigoto declarations
#define cmp -
Texture1D<float4> IniParams : register(t120);
Texture2D<float4> StereoParams : register(t125);


void main( 
  float4 v0 : SV_POSITION0,
  float2 v1 : TEXCOORD0,
  out float4 o0 : SV_Target0,
  out float4 o1 : SV_Target1)
{
// Needs manual fix for instruction: 
// unknown dcl_: dcl_resource_structured t4, 4 
  float4 r0,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10,r11,r12,r13,r14,r15,r16,r17,r18,r19,r20,r21,r22,r23,r24,r25,r26,r27,r28,r29,r30,r31,r32,r33,r34;
  uint4 bitmask, uiDest;
  float4 fDest;

  float4 x0[8];
  r0.xy = (uint2)v0.xy;
  r0.z = 0;
  r1.xyz = t14.Load(r0.xyz).xyz;
  r2.xyz = t15.Load(r0.xyz).xyz;
  r3.xyzw = t16.Load(r0.xyz).xyzw;
  r4.xy = r2.xy * float2(2,2) + float2(-1,-1);
  r1.w = dot(float2(1,1), abs(r4.xy));
  r5.y = 1 + -r1.w;
  r1.w = cmp(r5.y < 0);
  r4.zw = float2(1,1) + -abs(r4.yx);
  r6.xy = cmp(r4.xy >= float2(0,0));
  r6.xy = r6.xy ? float2(1,1) : float2(-1,-1);
  r4.zw = r6.xy * r4.zw;
  r5.xz = r1.ww ? r4.zw : r4.xy;
  r1.w = dot(r5.xyz, r5.xyz);
  r1.w = rsqrt(r1.w);
  r4.xyz = r5.xyz * r1.www;
  r5.xy = (uint2)r0.xy;
  r5.zw = float2(0.5,0.5) + r5.xy;
  r5.zw = cb0[62].zw * r5.zw;
  r1.w = t0.SampleLevel(s0_s, r5.zw, 0).x;
  r5.zw = cb0[62].zw * v0.xy;
  r6.xy = r5.zw * float2(2,2) + float2(-1,-1);
  r7.xyzw = cb0[21].xyzw * -r6.yyyy;
  r6.xyzw = cb0[20].xyzw * r6.xxxx + r7.xyzw;
  r6.xyzw = cb0[22].xyzw * r1.wwww + r6.xyzw;
  r6.xyzw = cb0[23].xyzw + r6.xyzw;
  r6.xyz = r6.xyz / r6.www;
  r1.w = cb0[1].z * r6.y;
  r1.w = cb0[0].z * r6.x + r1.w;
  r1.w = cb0[2].z * r6.z + r1.w;
  r1.w = cb0[3].z + r1.w;
  r2.w = cmp(cb0[66].w == 0.000000);
  r7.xyz = cb0[32].xyz + -r6.xyz;
  r8.x = cb0[0].z;
  r8.y = cb0[1].z;
  r8.z = cb0[2].z;
  r7.xyz = r2.www ? r7.xyz : r8.xyz;
  r2.w = dot(r7.xyz, r7.xyz);
  r7.w = rsqrt(r2.w);
  r9.xyz = r7.xyz * r7.www;
  r8.w = dot(r4.xyz, r9.xyz);
  r9.w = max(0, r8.w);
  r10.xyzw = r2.zzzz * float4(-1,-0.0274999999,-0.572000027,0.0219999999) + float4(1,0.0425000004,1.03999996,-0.0399999991);
  r11.x = r10.x * r10.x;
  r11.y = -9.27999973 * r9.w;
  r11.y = exp2(r11.y);
  r11.x = min(r11.x, r11.y);
  r10.x = r11.x * r10.x + r10.y;
  r10.xy = r10.xx * float2(-1.03999996,1.03999996) + r10.zw;
  r10.x = r10.x * 0.0399999991 + r10.y;
  r5.z = t9.SampleLevel(s1_s, r5.zw, 0).x;
  r5.w = max(r5.z, r1.z);
  r5.w = r5.w + -r5.z;
  r5.z = r3.w * r5.w + r5.z;
  r11.x = t7.Load(r0.xyz).x;
  r0.z = dot(-r9.xyz, r4.xyz);
  r0.z = r0.z + r0.z;
  r12.xyz = r4.xyz * -r0.zzz + -r9.xyz;
  r0.z = dot(-cb3[0].xyz, r12.xyz);
  r10.yzw = -r0.zzz * -cb3[0].xyz + r12.xyz;
  r0.z = cmp(r0.z < cb3[4].z);
  r5.w = dot(r10.yzw, r10.yzw);
  r5.w = rsqrt(r5.w);
  r10.yzw = r10.yzw * r5.www;
  r10.yzw = cb3[4].yyy * r10.yzw;
  r10.yzw = cb3[4].zzz * -cb3[0].xyz + r10.yzw;
  r5.w = dot(r10.yzw, r10.yzw);
  r5.w = rsqrt(r5.w);
  r10.yzw = r10.yzw * r5.www;
  r10.yzw = r0.zzz ? r10.yzw : r12.xyz;
  r0.z = dot(r10.yzw, r4.xyz);
  r13.xyz = r7.xyz * r7.www + r10.yzw;
  r5.w = dot(r13.xyz, r13.xyz);
  r5.w = rsqrt(r5.w);
  r13.xyz = r13.xyz * r5.www;
  r2.x = saturate(r0.z);
  r5.w = saturate(dot(r4.xyz, r13.xyz));
  r13.x = saturate(dot(r9.xyz, r13.xyz));
  r10.y = dot(r9.xyz, r10.yzw);
  r10.z = saturate(r10.y);
  r2.y = min(1, r9.w);
  r10.w = r2.z * r2.z;
  r13.y = r10.w * r10.w;
  r13.zw = -r2.yx * r13.yy + r2.yx;
  r13.zw = r13.zw * r2.yx + r13.yy;
  r13.zw = sqrt(r13.zw);
  r13.zw = r13.zw * r2.xy;
  r13.z = r13.z + r13.w;
  r13.z = 9.99999975e-005 + r13.z;
  r13.z = 0.5 / r13.z;
  r13.w = r5.w * r13.y + -r5.w;
  r5.w = r13.w * r5.w + 1;
  r5.w = r5.w * r5.w;
  r5.w = r13.y / r5.w;
  r13.x = 1 + -r13.x;
  r13.y = r13.x * r13.x;
  r13.y = r13.y * r13.y;
  r13.w = r13.y * r13.x;
  r13.x = -r13.y * r13.x + 1;
  r13.x = r13.x * 0.0399999991 + r13.w;
  r5.w = r5.w * r13.z;
  r5.w = r5.w * r13.x;
  r5.w = min(2048, r5.w);
  r13.xyzw = r2.yzxz * float4(0.96875,0.96875,0.96875,0.96875) + float4(0.015625,0.015625,0.015625,0.015625);
  r13.x = t8.SampleLevel(s4_s, r13.xy, 0).x;
  r13.y = t8.SampleLevel(s4_s, r13.zw, 0).x;
  r13.z = 1 + -r2.z;
  r13.w = -r13.z * 0.383026004 + -0.0761947036;
  r13.w = r13.z * r13.w + 1.04997003;
  r13.z = r13.z * r13.w + 0.409254998;
  r13.z = min(0.999000013, r13.z);
  r13.w = 1 + -r13.z;
  r13.y = r13.x * r13.y;
  r13.y = r13.y * r13.z;
  r13.y = r13.y / r13.w;
  r13.y = 0.0073469379 * r13.y;
  r14.x = -r13.w * 0.0857142806 + 1;
  r13.y = r13.y / r14.x;
  r5.w = r13.y + r5.w;
  r13.y = 0.5 + r10.z;
  r13.y = min(1, r13.y);
  r8.w = saturate(0.200000003 + r8.w);
  r14.y = r8.w * r13.y;
  r10.z = 3 * r10.z;
  r10.z = min(1, r10.z);
  r13.y = -r8.w * r13.y + 1;
  r10.z = r10.z * r13.y + r14.y;
  r5.w = r10.z * r5.w;
  r10.z = max(r3.x, r3.y);
  r10.z = max(r10.z, r3.z);
  r10.z = max(0.00999999978, r10.z);
  r14.yzw = r3.xyz / r10.zzz;
  r15.xyz = r14.yzw * r1.xxx;
  r10.z = saturate(0.5 + -r0.z);
  r10.y = saturate(-r10.y);
  r13.y = -0.639999986 * r10.y;
  r10.y = r13.y * r10.y + 1;
  r10.y = r10.y * r10.y;
  r13.y = cmp(r10.y != 0.360000);
  r10.y = 0.360000014 / r10.y;
  r10.y = r13.y ? r10.y : 1;
  r10.y = r10.z * r10.y;
  r0.z = saturate(r0.z + r3.w);
  r0.z = r0.z + -r2.x;
  r16.xyz = r3.xyz * r0.zzz;
  r16.xyz = r10.yyy * r15.xyz + r16.xyz;
  r0.z = r5.w * r1.z;
  r16.xyz = r16.xyz * r1.zzz;
  r0.z = cb3[4].x * r0.z;
  r0.z = max(0, r0.z);
  r0.z = min(1000, r0.z);
  r11.yw = float2(0.5,1);
  r17.xyz = t6.SampleBias(s1_s, r11.xy, cb0[88].x).xyz;
  r18.xyz = r3.xyz * r1.zzz + r0.zzz;
  r16.xyz = r18.xyz * r2.xxx + r16.xyz;
  r16.xyz = cb3[1].xyz * r16.xyz;
  r0.z = 1 + -r11.x;
  r17.xyz = r16.xyz * r17.xyz + -r16.xyz;
  r16.xyz = r0.zzz * r17.xyz + r16.xyz;
  r10.yz = (uint2)r0.xy >> int2(5,5);
  r0.z = mad((int)r10.z, asint(cb2[0].w), (int)r10.y);
  r2.x = (uint)r0.z << 3;
  r5.w = -cb0[65].y * cb2[2].w + abs(r1.w);
  r5.w = (int)r5.w;
  r10.y = (int)r5.w + asint(-cb2[1].y);
  r10.y = (int)r10.y + 1;
  r10.y = max(0, (int)r10.y);
  r10.y = min(1, (int)r10.y);
  r10.z = asint(cb2[1].y) + -1;
  r5.w = min((int)r10.z, (int)r5.w);
  r5.w = (uint)r5.w << 3;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.x, r2.x, l(0), t4.xxxx
r17.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.x = (((uint)r0.z << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.y = (((uint)r0.z << 3) & bitmask.y) | ((uint)2 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.z = (((uint)r0.z << 3) & bitmask.z) | ((uint)3 & ~bitmask.z);
  bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.w = (((uint)r0.z << 3) & bitmask.w) | ((uint)4 & ~bitmask.w);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.y, r18.x, l(0), t4.xxxx
r17.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.z, r18.y, l(0), t4.xxxx
r17.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.w, r18.z, l(0), t4.xxxx
r17.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.x, r18.w, l(0), t4.xxxx
r18.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.x = (((uint)r0.z << 3) & bitmask.x) | ((uint)5 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.y = (((uint)r0.z << 3) & bitmask.y) | ((uint)6 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.z = (((uint)r0.z << 3) & bitmask.z) | ((uint)7 & ~bitmask.z);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.y, r19.x, l(0), t4.xxxx
r18.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.z, r19.y, l(0), t4.xxxx
r18.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.w, r19.z, l(0), t4.xxxx
r18.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r0.z = (int)r5.w + asint(cb0[90].y);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r0.z, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r5.w = (int)-r10.y + 1;
  r19.x = (int)r2.x * (int)r5.w;
  r20.xyzw = (int4)r0.zzzz + int4(1,2,3,4);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.x, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.y = (int)r5.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.y, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.z = (int)r5.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.z, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.w = (int)r5.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.w, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.x = (int)r5.w * (int)r2.x;
  r21.xyz = (int3)r0.zzz + int3(5,6,7);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r21.x, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.y = (int)r5.w * (int)r0.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r21.y, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.z = (int)r5.w * (int)r0.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r21.z, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.w = (int)r5.w * (int)r0.z;
  r17.xyzw = (int4)r17.xyzw & (int4)r19.xyzw;
  r18.xyzw = (int4)r18.xyzw & (int4)r20.xyzw;
  x0[0].x = r17.x;
  x0[1].x = r17.y;
  x0[2].x = r17.z;
  x0[3].x = r17.w;
  x0[4].x = r18.x;
  x0[5].x = r18.y;
  x0[6].x = r18.z;
  x0[7].x = r18.w;
  r17.w = 1;
  r18.w = 1;
  r19.z = r2.z;
  r20.xyz = float3(0,0,0);
  r0.z = 1;
  r2.x = 0;
  while (true) {
    r5.w = cmp(7 < (uint)r2.x);
    if (r5.w != 0) break;
    r5.w = x0[r2.x+0].x;
    r10.y = (uint)r2.x << 5;
    r21.xyz = r20.xyz;
    r10.z = r0.z;
    r11.x = r5.w;
    while (true) {
      if (r11.x == 0) break;
      r11.y = firstbitlow((uint)r11.x);
      r13.y = (int)r10.y + (int)r11.y;
      r11.y = 1 << (int)r11.y;
      r11.y = (int)r11.y ^ (int)r11.x;
      bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.x = (((uint)r13.y << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)5 & ~bitmask.y);
      bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.z = (((uint)r13.y << 3) & bitmask.z) | ((uint)6 & ~bitmask.z);
      bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.w = (((uint)r13.y << 3) & bitmask.w) | ((uint)7 & ~bitmask.w);
      r15.w = (uint)cb3[r22.y+6].w;
      r15.w = cmp((int)r15.w == 1);
      if (r15.w != 0) {
        r23.xyz = asuint(cb3[r22.y+6].xyz) >> int3(16,16,16);
        r24.xyz = f16tof32(cb3[r22.y+6].xyz);
        r23.xyz = f16tof32(r23.xzy);
        r25.xyz = asuint(cb3[r22.z+6].xyz) >> int3(16,16,16);
        r26.xyz = f16tof32(cb3[r22.z+6].xyz);
        r25.xyw = f16tof32(r25.xyz);
        r17.xyz = -cb3[r22.x+6].xyz + r6.xyz;
        r27.xz = r24.xy;
        r27.yw = r23.xz;
        r15.w = dot(r17.xyzw, r27.xyzw);
        r23.x = r24.z;
        r23.z = r26.x;
        r23.w = r25.x;
        r16.w = dot(r17.xyzw, r23.xyzw);
        r25.xz = r26.yz;
        r17.x = dot(r17.xyzw, r25.xyzw);
        r15.w = max(abs(r16.w), abs(r15.w));
        r15.w = max(r15.w, abs(r17.x));
        r16.w = cmp(1 < r15.w);
        if (r16.w != 0) {
          r11.x = r11.y;
          continue;
        }
        r16.w = cb3[r22.w+6].x * 0.5 + 0.5;
        r15.w = -r16.w + r15.w;
        r16.w = 1 + -r16.w;
        r15.w = saturate(r15.w / r16.w);
        r15.w = 1 + -r15.w;
        r15.w = r15.w * r15.w;
      } else {
        r15.w = 1;
      }
      r16.w = (uint)r13.y << 3;
      bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.x = (((uint)r13.y << 3) & bitmask.x) | ((uint)2 & ~bitmask.x);
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)3 & ~bitmask.y);
      r13.y = (uint)cb3[r16.w+6].w;
      r17.z = cmp((uint)r13.y < 2);
      if (r17.z != 0) {
        r17.z = cmp(0.5 >= cb3[r17.y+6].z);
        if (r17.z != 0) {
          r17.z = cb3[r17.x+6].y * 0.5 + 0.5;
          r23.x = -abs(cb3[r17.x+6].x) + r17.z;
          r23.y = cb3[r17.x+6].y + -r23.x;
          r17.z = 1 + -abs(r23.x);
          r17.z = r17.z + -abs(r23.y);
          r17.z = max(5.96046448e-008, r17.z);
          r19.w = cmp(cb3[r17.x+6].x >= 0);
          r23.z = r19.w ? r17.z : -r17.z;
          r17.z = dot(r23.xyz, r23.xyz);
          r17.z = rsqrt(r17.z);
          r23.xyz = r23.xyz * r17.zzz;
          r24.xyz = cb3[r22.x+6].xyz + -r6.xyz;
          r17.z = dot(r24.xyz, r24.xyz);
          r19.w = rsqrt(r17.z);
          r18.xyz = r24.xyz * r19.www;
          r25.xyz = cb3[r17.x+6].zzz * r23.xyz;
          r26.xyz = -r25.xyz * float3(0.5,0.5,0.5) + r24.xyz;
          r27.xyz = r25.xyz * float3(0.5,0.5,0.5) + r24.xyz;
          r20.w = (int)r13.y & 1;
          r21.w = cmp((int)r20.w != 0);
          r22.y = cmp(0 < cb3[r17.x+6].z);
          r21.w = r21.w ? r22.y : 0;
          r11.z = saturate(dot(r4.xyz, r18.xyz));
          r22.y = dot(r26.xyz, r26.xyz);
          r22.y = sqrt(r22.y);
          r23.w = dot(r27.xyz, r27.xyz);
          r23.w = sqrt(r23.w);
          r24.w = dot(r26.xyz, r27.xyz);
          r24.w = r22.y * r23.w + r24.w;
          r24.w = r24.w * 0.5 + 1;
          r28.y = rcp(r24.w);
          r24.w = dot(r4.xyz, r26.xyz);
          r22.y = r24.w / r22.y;
          r24.w = dot(r4.xyz, r27.xyz);
          r23.w = r24.w / r23.w;
          r22.y = r23.w + r22.y;
          r28.x = saturate(0.5 * r22.y);
          r19.xy = r21.ww ? r28.xy : r11.zw;
          r11.z = cmp(cb3[r22.z+6].w < 0);
          r22.y = 1 + r17.z;
          r22.y = 1 / r22.y;
          r23.w = r21.w ? 1.000000 : 0;
          r24.w = -r22.y + r19.y;
          r22.y = r23.w * r24.w + r22.y;
          r23.w = cb3[r22.x+6].w * cb3[r22.x+6].w;
          r17.z = r23.w * r17.z;
          r17.z = -r17.z * r17.z + 1;
          r17.z = max(0, r17.z);
          r17.z = r17.z * r17.z;
          r17.z = r22.y * r17.z;
          r24.xyz = cb3[r22.x+6].www * r24.xyz;
          r22.y = dot(r24.xyz, r24.xyz);
          r22.y = min(1, r22.y);
          r22.y = 1 + -r22.y;
          r22.y = log2(r22.y);
          r22.y = cb3[r22.z+6].w * r22.y;
          r22.y = exp2(r22.y);
          r19.y = r22.y * r19.y;
          r11.z = r11.z ? r17.z : r19.y;
          r17.z = dot(r18.xyz, -r23.xyz);
          r17.z = -cb3[r17.x+6].z + r17.z;
          r17.z = saturate(cb3[r17.x+6].w * r17.z);
          r17.z = r17.z * r17.z;
          r17.z = r17.z * r11.z;
          r11.z = r20.w ? r11.z : r17.z;
          r17.z = cmp(0 < r11.z);
          if (r17.z != 0) {
            r17.z = (int)cb3[r17.y+6].x;
            r23.xyz = -cb3[r22.x+6].xyz + r6.xyz;
            r24.xyz = cmp(abs(r23.yzz) < abs(r23.xxy));
            r19.y = r24.y ? r24.x : 0;
            r24.xyw = cmp(float3(0,0,0) < r23.xyz);
            r22.y = asuint(cb3[r17.x+6].w) >> 24;
            if (8 == 0) r27.x = 0; else if (8+16 < 32) {             r27.x = (uint)cb3[r17.x+6].w << (32-(8 + 16)); r27.x = (uint)r27.x >> (32-8);            } else r27.x = (uint)cb3[r17.x+6].w >> 16;
            if (8 == 0) r27.y = 0; else if (8+8 < 32) {             r27.y = (uint)cb3[r17.x+6].w << (32-(8 + 8)); r27.y = (uint)r27.y >> (32-8);            } else r27.y = (uint)cb3[r17.x+6].w >> 8;
            r22.y = r24.x ? r22.y : r27.x;
            r23.w = 255 & asint(cb3[r17.x+6].w);
            r23.w = r24.y ? r27.y : r23.w;
            if (8 == 0) r24.x = 0; else if (8+8 < 32) {             r24.x = (uint)cb3[r17.y+6].x << (32-(8 + 8)); r24.x = (uint)r24.x >> (32-8);            } else r24.x = (uint)cb3[r17.y+6].x >> 8;
            r24.y = 255 & asint(cb3[r17.y+6].x);
            r24.x = r24.w ? r24.x : r24.y;
            r23.w = r24.z ? r23.w : r24.x;
            r19.y = r19.y ? r22.y : r23.w;
            r22.y = cmp((int)r19.y < 80);
            r19.y = r22.y ? r19.y : -1;
            r17.z = r20.w ? r19.y : r17.z;
            r19.y = cmp((int)r17.z >= 0);
            r20.w = dot(r23.xyz, r23.xyz);
            r20.w = max(1.17549435e-038, r20.w);
            r20.w = rsqrt(r20.w);
            r23.xyz = r23.xyz * r20.www;
            r20.w = dot(r4.xyz, r23.xyz);
            r20.w = max(0, r20.w);
            r20.w = min(0.899999976, r20.w);
            r20.w = 1 + -r20.w;
            r24.xy = cb4[r17.z+256].xy * r20.ww;
            r20.w = 5 * r24.y;
            r23.xyz = -r23.xyz * r24.xxx + r6.xyz;
            r23.xyz = r4.xyz * r20.www + r23.xyz;
            r20.w = (uint)r17.z << 2;
            r24.xyzw = cb4[r20.w+33].xyzw * r23.yyyy;
            r24.xyzw = cb4[r20.w+32].xyzw * r23.xxxx + r24.xyzw;
            r23.xyzw = cb4[r20.w+34].xyzw * r23.zzzz + r24.xyzw;
            r23.xyzw = cb4[r20.w+35].xyzw + r23.xyzw;
            r23.xyz = r23.xyz / r23.www;
            r24.xy = cb4[r17.z+312].zw + -cb4[r17.z+312].xy;
            r24.xy = r23.xy * r24.xy + cb4[r17.z+312].xy;
            r27.xyz = cmp(float3(0,0,0) >= r23.xyz);
            r23.xyw = cmp(r23.xyz >= float3(1,1,1));
            r23.xyw = (int3)r23.xyw | (int3)r27.xyz;
            r20.w = (int)r23.y | (int)r23.x;
            r20.w = (int)r23.w | (int)r20.w;
            r22.y = (int)r23.z & 0x7fffffff;
            r22.y = cmp(0x7f800000 < (uint)r22.y);
            r20.w = (int)r20.w | (int)r22.y;
            r23.xy = r24.xy * cb4[368].zw + float2(0.5,0.5);
            r23.xy = floor(r23.xy);
            r24.xy = r24.xy * cb4[368].zw + -r23.xy;
            r27.xyzw = float4(0.5,1,0.5,1) + r24.xxyy;
            r28.xyzw = r27.xxzz * r27.xxzz;
            r24.zw = float2(0.0799999982,0.0799999982) * r28.yw;
            r27.xz = r28.xz * float2(0.5,0.5) + -r24.xy;
            r28.xy = float2(1,1) + -r24.xy;
            r28.zw = min(float2(0,0), r24.xy);
            r28.zw = -r28.zw * r28.zw + r28.xy;
            r24.xy = max(float2(0,0), r24.xy);
            r24.xy = -r24.xy * r24.xy + r27.yw;
            r28.zw = float2(1,1) + r28.zw;
            r24.xy = float2(1,1) + r24.xy;
            r29.xy = float2(0.159999996,0.159999996) * r27.xz;
            r30.xy = float2(0.159999996,0.159999996) * r28.xy;
            r28.xy = float2(0.159999996,0.159999996) * r28.zw;
            r31.xy = float2(0.159999996,0.159999996) * r24.xy;
            r24.xy = float2(0.159999996,0.159999996) * r27.yw;
            r29.z = r28.x;
            r29.w = r24.x;
            r30.z = r31.x;
            r30.w = r24.z;
            r27.xyzw = r30.zwxz + r29.zwxz;
            r28.z = r29.y;
            r28.w = r24.y;
            r31.z = r30.y;
            r31.w = r24.w;
            r24.xyz = r31.zyw + r28.zyw;
            r28.xyz = r30.xzw / r27.zwy;
            r28.xyz = float3(-2.5,-0.5,1.5) + r28.xyz;
            r29.xyz = r31.zyw / r24.xyz;
            r29.xyz = float3(-2.5,-0.5,1.5) + r29.xyz;
            r28.xyz = cb4[368].xxx * r28.yxz;
            r29.xyz = cb4[368].yyy * r29.xyz;
            r28.w = r29.x;
            r30.xyzw = r23.xyxy * cb4[368].xyxy + r28.ywxw;
            r31.xy = r23.xy * cb4[368].xy + r28.zw;
            r29.w = r28.y;
            r28.yw = r29.yz;
            r32.xyzw = r23.xyxy * cb4[368].xyxy + r28.xyzy;
            r29.xyzw = r23.xyxy * cb4[368].xyxy + r29.wywz;
            r28.xyzw = r23.xyxy * cb4[368].xyxy + r28.xwzw;
            r33.xyzw = r27.zwyz * r24.xxxy;
            r34.xyzw = r27.xyzw * r24.yyzz;
            r22.y = r27.y * r24.z;
            r23.x = t5.SampleCmpLevelZero(s3_s, r30.xy, r23.z).x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r30.zw, r23.z).x;
            r23.y = r33.y * r23.y;
            r23.x = r33.x * r23.x + r23.y;
            r23.y = t5.SampleCmpLevelZero(s3_s, r31.xy, r23.z).x;
            r23.x = r33.z * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r29.xy, r23.z).x;
            r23.x = r33.w * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r32.xy, r23.z).x;
            r23.x = r34.x * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r32.zw, r23.z).x;
            r23.x = r34.y * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r29.zw, r23.z).x;
            r23.x = r34.z * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r28.xy, r23.z).x;
            r23.x = r34.w * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r28.zw, r23.z).x;
            r22.y = r22.y * r23.y + r23.x;
            r22.y = -1 + r22.y;
            r17.z = cb4[r17.z+256].w * r22.y + 1;
            r17.z = r20.w ? 1 : r17.z;
            r17.z = r19.y ? r17.z : 1;
            r19.y = saturate(cb3[r22.w+6].y * r19.w);
            r19.w = saturate(cb3[r17.x+6].z * r19.w);
            r19.w = r19.w * 0.5 + r10.w;
            r19.w = min(1, r19.w);
            r23.w = r10.w / r19.w;
            r19.w = dot(r12.xyz, r25.xyz);
            r24.xyz = r19.www * r12.xyz + -r25.xyz;
            r20.w = dot(r26.xyz, r24.xyz);
            r19.w = r19.w * r19.w;
            r19.w = cb3[r17.x+6].z * cb3[r17.x+6].z + -r19.w;
            r19.w = saturate(r20.w / r19.w);
            r24.xyz = r19.www * r25.xyz + r26.xyz;
            r19.w = dot(r24.xyz, r24.xyz);
            r19.w = rsqrt(r19.w);
            r23.xyz = r24.xyz * r19.www;
            r23.xyzw = r21.wwww ? r23.xyzw : r18.xyzw;
            r18.xyz = r7.xyz * r7.www + r23.xyz;
            r19.w = dot(r18.xyz, r18.xyz);
            r19.w = rsqrt(r19.w);
            r18.xyz = r19.www * r18.xyz;
            r19.w = dot(r4.xyz, r23.xyz);
            r20.w = saturate(dot(r4.xyz, r18.xyz));
            r18.x = saturate(dot(r9.xyz, r18.xyz));
            r18.y = dot(r9.xyz, r23.xyz);
            r18.z = saturate(r18.y);
            r21.w = cmp(0 < r19.y);
            r19.y = r19.y * r19.y;
            r22.y = r18.x * 3.5999999 + 0.400000006;
            r19.y = r19.y / r22.y;
            r19.y = r2.z * r2.z + r19.y;
            r19.y = min(1, r19.y);
            r19.y = r21.w ? r19.y : r10.w;
            r19.y = r19.y * r19.y;
            r21.w = -r2.y * r19.y + r2.y;
            r21.w = r21.w * r2.y + r19.y;
            r21.w = sqrt(r21.w);
            r22.y = -r19.x * r19.y + r19.x;
            r22.y = r22.y * r19.x + r19.y;
            r22.y = sqrt(r22.y);
            r22.y = r22.y * r2.y;
            r21.w = r19.x * r21.w + r22.y;
            r21.w = 9.99999975e-005 + r21.w;
            r21.w = 0.5 / r21.w;
            r22.y = r20.w * r19.y + -r20.w;
            r20.w = r22.y * r20.w + 1;
            r20.w = r20.w * r20.w;
            r19.y = r19.y / r20.w;
            r19.y = r19.y * r23.w;
            r18.x = 1 + -r18.x;
            r20.w = r18.x * r18.x;
            r20.w = r20.w * r20.w;
            r22.y = r20.w * r18.x;
            r18.x = -r20.w * r18.x + 1;
            r18.x = r18.x * 0.0399999991 + r22.y;
            r19.y = r19.y * r21.w;
            r18.x = r19.y * r18.x;
            r18.x = min(2048, r18.x);
            r23.xy = r19.xz * float2(0.96875,0.96875) + float2(0.015625,0.015625);
            r19.y = t8.SampleLevel(s4_s, r23.xy, 0).x;
            r19.y = r19.y * r13.x;
            r19.y = r19.y * r13.z;
            r19.y = r19.y / r13.w;
            r19.y = 0.0073469379 * r19.y;
            r19.y = r19.y / r14.x;
            r18.x = r19.y + r18.x;
            r19.y = 0.5 + r18.z;
            r19.y = min(1, r19.y);
            r20.w = r19.y * r8.w;
            r18.z = 3 * r18.z;
            r18.z = min(1, r18.z);
            r19.y = -r8.w * r19.y + 1;
            r18.z = r18.z * r19.y + r20.w;
            r18.x = r18.x * r18.z;
            r18.z = saturate(0.5 + -r19.w);
            r18.y = saturate(-r18.y);
            r19.y = -0.639999986 * r18.y;
            r18.y = r19.y * r18.y + 1;
            r18.y = r18.y * r18.y;
            r19.y = cmp(r18.y != 0.360000);
            r18.y = 0.360000014 / r18.y;
            r18.y = r19.y ? r18.y : 1;
            r18.y = r18.z * r18.y;
            r18.z = saturate(r19.w + r3.w);
            r18.z = r18.z + -r19.x;
            r23.xyz = r18.zzz * r3.xyz;
            r23.xyz = r18.yyy * r15.xyz + r23.xyz;
            r18.x = r18.x * r1.z;
            r23.xyz = r23.xyz * r1.zzz;
            r18.x = cb3[r22.w+6].z * r18.x;
            r18.x = max(0, r18.x);
            r18.x = min(1000, r18.x);
            r24.xyz = cb3[r16.w+6].xyz * r11.zzz;
            r24.xyz = r24.xyz * r17.zzz;
            r24.xyz = r24.xyz * r15.www;
            r18.xyz = r3.xyz * r1.zzz + r18.xxx;
            r18.xyz = r18.xyz * r19.xxx + r23.xyz;
            r18.xyz = r24.xyz * r18.xyz;
          } else {
            r18.xyz = float3(0,0,0);
          }
        } else {
          r18.xyz = float3(0,0,0);
        }
        r21.xyz = r21.xyz + r18.xyz;
      } else {
        r11.z = cmp(0.5 >= cb3[r17.y+6].z);
        if (r11.z != 0) {
          r11.z = cb3[r17.x+6].y * 0.5 + 0.5;
          r18.x = -abs(cb3[r17.x+6].x) + r11.z;
          r18.y = cb3[r17.x+6].y + -r18.x;
          r11.z = 1 + -abs(r18.x);
          r11.z = r11.z + -abs(r18.y);
          r11.z = max(5.96046448e-008, r11.z);
          r15.w = cmp(cb3[r17.x+6].x >= 0);
          r18.z = r15.w ? r11.z : -r11.z;
          r11.z = dot(r18.xyz, r18.xyz);
          r11.z = rsqrt(r11.z);
          r18.xyz = r18.xyz * r11.zzz;
          r19.xyw = cb3[r22.x+6].xyz + -r6.xyz;
          r11.z = dot(r19.xyw, r19.xyw);
          r15.w = rsqrt(r11.z);
          r23.xyz = r19.xyw * r15.www;
          r24.xyz = cb3[r17.x+6].zzz * r18.xyz;
          r25.xyz = -r24.xyz * float3(0.5,0.5,0.5) + r19.xyw;
          r24.xyz = r24.xyz * float3(0.5,0.5,0.5) + r19.xyw;
          r13.y = (int)r13.y & 1;
          r15.w = cmp((int)r13.y != 0);
          r16.w = cmp(0 < cb3[r17.x+6].z);
          r15.w = r15.w ? r16.w : 0;
          r16.w = dot(r25.xyz, r25.xyz);
          r16.w = sqrt(r16.w);
          r17.z = dot(r24.xyz, r24.xyz);
          r17.z = sqrt(r17.z);
          r20.w = dot(r25.xyz, r24.xyz);
          r16.w = r16.w * r17.z + r20.w;
          r16.w = r16.w * 0.5 + 1;
          r16.w = rcp(r16.w);
          r16.w = r15.w ? r16.w : 1;
          r17.z = cmp(cb3[r22.z+6].w < 0);
          r20.w = 1 + r11.z;
          r20.w = 1 / r20.w;
          r15.w = r15.w ? 1.000000 : 0;
          r21.w = -r20.w + r16.w;
          r15.w = r15.w * r21.w + r20.w;
          r20.w = cb3[r22.x+6].w * cb3[r22.x+6].w;
          r11.z = r20.w * r11.z;
          r11.z = -r11.z * r11.z + 1;
          r11.z = max(0, r11.z);
          r11.z = r11.z * r11.z;
          r11.z = r15.w * r11.z;
          r19.xyw = cb3[r22.x+6].www * r19.xyw;
          r15.w = dot(r19.xyw, r19.xyw);
          r15.w = min(1, r15.w);
          r15.w = 1 + -r15.w;
          r15.w = log2(r15.w);
          r15.w = cb3[r22.z+6].w * r15.w;
          r15.w = exp2(r15.w);
          r15.w = r16.w * r15.w;
          r11.z = r17.z ? r11.z : r15.w;
          r15.w = dot(r23.xyz, -r18.xyz);
          r15.w = -cb3[r17.x+6].z + r15.w;
          r15.w = saturate(cb3[r17.x+6].w * r15.w);
          r15.w = r15.w * r15.w;
          r15.w = r15.w * r11.z;
          r11.z = r13.y ? r11.z : r15.w;
          r11.z = cmp(0 < r11.z);
          if (r11.z != 0) {
            r11.z = (int)cb3[r17.y+6].x;
            r18.xyz = -cb3[r22.x+6].xyz + r6.xyz;
            r19.xyw = cmp(abs(r18.yzz) < abs(r18.xxy));
            r15.w = r19.y ? r19.x : 0;
            r22.xyz = cmp(float3(0,0,0) < r18.xyz);
            r16.w = asuint(cb3[r17.x+6].w) >> 24;
            if (8 == 0) r19.x = 0; else if (8+16 < 32) {             r19.x = (uint)cb3[r17.x+6].w << (32-(8 + 16)); r19.x = (uint)r19.x >> (32-8);            } else r19.x = (uint)cb3[r17.x+6].w >> 16;
            if (8 == 0) r19.y = 0; else if (8+8 < 32) {             r19.y = (uint)cb3[r17.x+6].w << (32-(8 + 8)); r19.y = (uint)r19.y >> (32-8);            } else r19.y = (uint)cb3[r17.x+6].w >> 8;
            r16.w = r22.x ? r16.w : r19.x;
            r17.x = 255 & asint(cb3[r17.x+6].w);
            r17.x = r22.y ? r19.y : r17.x;
            if (8 == 0) r17.z = 0; else if (8+8 < 32) {             r17.z = (uint)cb3[r17.y+6].x << (32-(8 + 8)); r17.z = (uint)r17.z >> (32-8);            } else r17.z = (uint)cb3[r17.y+6].x >> 8;
            r17.y = 255 & asint(cb3[r17.y+6].x);
            r17.y = r22.z ? r17.z : r17.y;
            r17.x = r19.w ? r17.x : r17.y;
            r15.w = r15.w ? r16.w : r17.x;
            r16.w = cmp((int)r15.w < 80);
            r15.w = r16.w ? r15.w : -1;
            r11.z = r13.y ? r15.w : r11.z;
            r13.y = cmp((int)r11.z >= 0);
            r15.w = dot(r18.xyz, r18.xyz);
            r15.w = max(1.17549435e-038, r15.w);
            r15.w = rsqrt(r15.w);
            r17.xyz = r18.xyz * r15.www;
            r15.w = dot(r4.xyz, r17.xyz);
            r15.w = max(0, r15.w);
            r15.w = min(0.899999976, r15.w);
            r15.w = 1 + -r15.w;
            r18.xy = cb4[r11.z+256].xy * r15.ww;
            r15.w = 5 * r18.y;
            r17.xyz = -r17.xyz * r18.xxx + r6.xyz;
            r17.xyz = r4.xyz * r15.www + r17.xyz;
            r15.w = (uint)r11.z << 2;
            r22.xyzw = cb4[r15.w+33].xyzw * r17.yyyy;
            r22.xyzw = cb4[r15.w+32].xyzw * r17.xxxx + r22.xyzw;
            r22.xyzw = cb4[r15.w+34].xyzw * r17.zzzz + r22.xyzw;
            r22.xyzw = cb4[r15.w+35].xyzw + r22.xyzw;
            r17.xyz = r22.xyz / r22.www;
            r18.xy = cb4[r11.z+312].zw + -cb4[r11.z+312].xy;
            r18.xy = r17.xy * r18.xy + cb4[r11.z+312].xy;
            r19.xyw = cmp(float3(0,0,0) >= r17.xyz);
            r22.xyz = cmp(r17.xyz >= float3(1,1,1));
            r19.xyw = (int3)r19.xyw | (int3)r22.xyz;
            r15.w = (int)r19.y | (int)r19.x;
            r15.w = (int)r19.w | (int)r15.w;
            r16.w = (int)r17.z & 0x7fffffff;
            r16.w = cmp(0x7f800000 < (uint)r16.w);
            r15.w = (int)r15.w | (int)r16.w;
            r17.xy = r18.xy * cb4[368].zw + float2(0.5,0.5);
            r17.xy = floor(r17.xy);
            r18.xy = r18.xy * cb4[368].zw + -r17.xy;
            r22.xyzw = float4(0.5,1,0.5,1) + r18.xxyy;
            r23.xyzw = r22.xxzz * r22.xxzz;
            r19.xy = float2(0.0799999982,0.0799999982) * r23.yw;
            r22.xz = r23.xz * float2(0.5,0.5) + -r18.xy;
            r23.xy = float2(1,1) + -r18.xy;
            r23.zw = min(float2(0,0), r18.xy);
            r23.zw = -r23.zw * r23.zw + r23.xy;
            r18.xy = max(float2(0,0), r18.xy);
            r18.xy = -r18.xy * r18.xy + r22.yw;
            r23.zw = float2(1,1) + r23.zw;
            r18.xy = float2(1,1) + r18.xy;
            r24.xy = float2(0.159999996,0.159999996) * r22.xz;
            r25.xy = float2(0.159999996,0.159999996) * r23.xy;
            r23.xy = float2(0.159999996,0.159999996) * r23.zw;
            r26.xy = float2(0.159999996,0.159999996) * r18.xy;
            r18.xy = float2(0.159999996,0.159999996) * r22.yw;
            r24.z = r23.x;
            r24.w = r18.x;
            r25.z = r26.x;
            r25.w = r19.x;
            r22.xyzw = r25.zwxz + r24.zwxz;
            r23.z = r24.y;
            r23.w = r18.y;
            r26.z = r25.y;
            r26.w = r19.y;
            r18.xyz = r26.zyw + r23.zyw;
            r19.xyw = r25.xzw / r22.zwy;
            r19.xyw = float3(-2.5,-0.5,1.5) + r19.xyw;
            r23.xyz = r26.zyw / r18.xyz;
            r23.xyz = float3(-2.5,-0.5,1.5) + r23.xyz;
            r24.xyz = cb4[368].xxx * r19.yxw;
            r23.xyz = cb4[368].yyy * r23.xyz;
            r24.w = r23.x;
            r25.xyzw = r17.xyxy * cb4[368].xyxy + r24.ywxw;
            r19.xy = r17.xy * cb4[368].xy + r24.zw;
            r23.w = r24.y;
            r24.yw = r23.yz;
            r26.xyzw = r17.xyxy * cb4[368].xyxy + r24.xyzy;
            r23.xyzw = r17.xyxy * cb4[368].xyxy + r23.wywz;
            r24.xyzw = r17.xyxy * cb4[368].xyxy + r24.xwzw;
            r27.xyzw = r22.zwyz * r18.xxxy;
            r28.xyzw = r22.xyzw * r18.yyzz;
            r16.w = r22.y * r18.z;
            r17.x = t5.SampleCmpLevelZero(s3_s, r25.xy, r17.z).x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r25.zw, r17.z).x;
            r17.y = r27.y * r17.y;
            r17.x = r27.x * r17.x + r17.y;
            r17.y = t5.SampleCmpLevelZero(s3_s, r19.xy, r17.z).x;
            r17.x = r27.z * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r23.xy, r17.z).x;
            r17.x = r27.w * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r26.xy, r17.z).x;
            r17.x = r28.x * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r26.zw, r17.z).x;
            r17.x = r28.y * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r23.zw, r17.z).x;
            r17.x = r28.z * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r24.xy, r17.z).x;
            r17.x = r28.w * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r24.zw, r17.z).x;
            r16.w = r16.w * r17.y + r17.x;
            r16.w = -1 + r16.w;
            r11.z = cb4[r11.z+256].w * r16.w + 1;
            r11.z = r15.w ? 1 : r11.z;
            r11.z = r13.y ? r11.z : 1;
          } else {
            r11.z = 1;
          }
        } else {
          r11.z = 1;
        }
        r10.z = r11.z * r10.z;
      }
      r11.x = r11.y;
    }
    r20.xyz = r21.xyz;
    r0.z = r10.z;
    r2.x = (int)r2.x + 1;
  }
  r7.xyz = r20.xyz * r0.zzz;
  r7.xyz = r16.xyz * r5.zzz + r7.xyz;
  r5.zw = cmp(cb0[92].xy != float2(0,0));
  if (r5.z != 0) {
    r0.z = t2.SampleBias(s0_s, v1.xy, cb0[88].x).x;
    r0.z = min(r0.z, r1.y);
    r1.z = r9.w + r0.z;
    r2.x = r2.z * -16 + -1;
    r2.x = exp2(r2.x);
    r1.z = log2(abs(r1.z));
    r1.z = r2.x * r1.z;
    r1.z = exp2(r1.z);
    r1.z = -1 + r1.z;
    r1.z = r1.z + r0.z;
    r10.yzw = saturate(r1.zzz);
    r11.xyz = r3.xyz * float3(2.04040003,2.04040003,2.04040003) + float3(-0.332399994,-0.332399994,-0.332399994);
    r13.xyz = r3.xyz * float3(-4.79510021,-4.79510021,-4.79510021) + float3(0.641700029,0.641700029,0.641700029);
    r15.xyz = r3.xyz * float3(2.75519991,2.75519991,2.75519991) + float3(0.690299988,0.690299988,0.690299988);
    r11.xyz = r0.zzz * r11.xyz + r13.xyz;
    r11.xyz = r11.xyz * r0.zzz + r15.xyz;
    r11.xyz = r11.xyz * r0.zzz;
    r11.xyz = max(r11.xyz, r0.zzz);
  } else {
    r10.yzw = r1.yyy;
    r11.xyz = r1.yyy;
  }
  r13.xyz = -cb0[175].xyz + r6.xyz;
  r0.z = max(abs(r13.x), abs(r13.y));
  r0.z = max(r0.z, abs(r13.z));
  r1.y = -896 + r0.z;
  r1.y = saturate(0.015625 * r1.y);
  r1.z = cmp(0 < cb0[175].w);
  r2.x = cmp(r1.y < 1);
  r1.z = r1.z ? r2.x : 0;
  if (r1.z != 0) {
    r13.xy = float2(-100,-200) + r0.zz;
    r13.xy = saturate(float2(0.0833333358,0.0625) * r13.xy);
    r13.xy = cmp(r13.xy < float2(1,1));
    r13.yz = r13.yy ? float2(0.001953125,1) : float2(0.00048828125,2);
    r13.xy = r13.xx ? float2(0.00390625,0) : r13.yz;
    r13.xzw = r13.xxx * r6.xyz;
    r13.xzw = frac(r13.xzw);
    r13.xyzw = t11.SampleLevel(s0_s, r13.xzw, r13.y).xyzw;
    r13.xyzw = r13.xyzw * float4(255,255,255,255) + float4(0.5,0.5,0.5,0.5);
    r13.xyzw = floor(r13.xyzw);
    r0.z = cmp(0 < r13.w);
    if (r0.z != 0) {
      r15.xyz = r6.xyz / r13.www;
      r15.xyz = frac(r15.xyz);
      r15.xyz = r15.xyz * float3(4,4,4) + float3(0.5,0.5,0.5);
      r13.xyz = r13.xyz * float3(5,5,5) + r15.xyz;
      r13.xyz = cb0[176].xyz * r13.xyz;
      r15.xyz = t12.SampleLevel(s1_s, r13.xyz, 0).xyz;
      r13.w = 0.333333343 * r13.z;
      r16.xyz = t13.SampleLevel(s1_s, r13.xyw, 0).xyz;
      r17.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.333333343);
      r17.xyz = t13.SampleLevel(s1_s, r17.xyz, 0).xyz;
      r13.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.666666687);
      r13.xyz = t13.SampleLevel(s1_s, r13.xyz, 0).xyz;
      r16.xyz = r16.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r16.xyz = r16.xyz * r15.xxx;
      r17.xyz = r17.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r17.xyz = r17.xyz * r15.yyy;
      r13.xyz = r13.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r13.xyz = r15.zzz * r13.xyz;
      r16.w = r15.x;
      r18.xyzw = cb0[178].xyzw + -r16.xyzw;
      r16.xyzw = r1.yyyy * r18.xyzw + r16.xyzw;
      r17.w = r15.y;
      r18.xyzw = cb0[179].xyzw + -r17.xyzw;
      r17.xyzw = r1.yyyy * r18.xyzw + r17.xyzw;
      r13.w = r15.z;
      r15.xyzw = cb0[180].xyzw + -r13.xyzw;
      r13.xyzw = r1.yyyy * r15.xyzw + r13.xyzw;
    } else {
      r16.xyzw = cb0[178].xyzw;
      r17.xyzw = cb0[179].xyzw;
      r13.xyzw = cb0[180].xyzw;
    }
  } else {
    r16.xyzw = cb0[178].xyzw;
    r17.xyzw = cb0[179].xyzw;
    r13.xyzw = cb0[180].xyzw;
  }
  r4.w = 1;
  r15.x = dot(r16.xyzw, r4.xyzw);
  r15.y = dot(r17.xyzw, r4.xyzw);
  r15.z = dot(r13.xyzw, r4.xyzw);
  r15.xyz = max(float3(0,0,0), r15.xyz);
  r4.xyzw = float4(-1,-1,-1,1) * r4.xyzw;
  r16.x = dot(r16.xyzw, r4.xyzw);
  r16.y = dot(r17.xyzw, r4.xyzw);
  r16.z = dot(r13.xyzw, r4.xyzw);
  r4.xyz = max(float3(0,0,0), r16.xyz);
  r3.xyz = r15.xyz * r3.xyz;
  r3.xyz = cb0[91].xxx * r3.xyz;
  r0.z = max(0.00100000005, r2.z);
  r0.z = log2(r0.z);
  r0.z = -r0.z * 1.20000005 + 1;
  r0.z = 6 + -r0.z;
  r1.yz = (uint2)cb1[0].wx;
  uiDest.xz = (uint2)r0.xy / (uint2)r1.yy;
  r2.xz = uiDest.xz;
  r1.y = mad((int)r2.z, (int)r1.z, (int)r2.x);
  r2.xz = (uint2)cb1[1].xy;
  r1.z = (uint)r2.z;
  r1.z = cb1[2].y / r1.z;
  r1.z = abs(r1.w) + -r1.z;
  r1.z = (int)r1.z;
  r2.z = (int)-r2.x + (int)r1.z;
  r2.xz = (int2)r2.xz + int2(-1,1);
  r2.z = min(1, (uint)r2.z);
  r1.z = min((uint)r2.x, (uint)r1.z);
  r1.yz = (int2)r1.yz + asint(cb0[90].zw);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.y, r1.y, l(0), t4.xxxx
r1.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r1.z, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r1.z = (int)r2.z * (int)r1.z;
  r1.y = (int)r1.z & (int)r1.y;
  r1.z = dot(r15.xyz, float3(0.212672904,0.715152204,0.0721750036));
  r6.w = 1;
  r13.w = 1;
  r15.xyz = float3(0,0,0);
  r2.x = r1.y;
  r2.z = 0;
  r3.w = -1;
  r4.w = 0;
  while (true) {
    if (r2.x == 0) break;
    r5.z = firstbitlow((uint)r2.x);
    r7.w = 1 << (int)r5.z;
    r2.x = (int)r2.x ^ (int)r7.w;
    r5.z = (int)r5.z * 6;
    r16.x = dot(cb1[r5.z+6].xyzw, r6.xyzw);
    r16.y = dot(cb1[r5.z+7].xyzw, r6.xyzw);
    r16.z = dot(cb1[r5.z+8].xyzw, r6.xyzw);
    r17.xyz = r16.xyz / cb1[r5.z+5].xyz;
    r7.w = dot(abs(r17.xyz), abs(r17.xyz));
    r7.w = sqrt(r7.w);
    r8.w = cmp(cb1[r5.z+9].w < 0);
    r9.w = cmp(r7.w < 1);
    r11.w = max(abs(r17.y), abs(r17.z));
    r11.w = max(abs(r17.x), r11.w);
    r11.w = cmp(r11.w < 1);
    r9.w = r8.w ? r9.w : r11.w;
    if (r9.w != 0) {
      r9.w = f16tof32(cb1[r5.z+5].w);
      r11.w = asuint(cb1[r5.z+5].w) >> 16;
      r11.w = f16tof32(r11.w);
      r18.w = abs(r11.w);
      r11.w = cmp(0 < r11.w);
      r7.w = -cb1[r5.z+9].x + r7.w;
      r17.xyz = -cb1[r5.z+9].xyz + abs(r17.xyz);
      r17.xyz = r8.www ? r7.www : r17.xyz;
      r19.xyz = -cb1[r5.z+9].xyz + float3(1,1,1);
      r17.xyz = saturate(r17.xyz / r19.xyz);
      r17.xyz = float3(1,1,1) + -r17.xyz;
      r7.w = min(r17.y, r17.z);
      r7.w = min(r17.x, r7.w);
      r7.w = r8.w ? r17.x : r7.w;
      r14.x = cmp(r9.w < r3.w);
      r15.w = 1 + -r4.w;
      r7.w = r14.x ? r15.w : r7.w;
      if (r8.w != 0) {
        r17.x = dot(cb1[r5.z+6].xyz, r12.xyz);
        r17.y = dot(cb1[r5.z+7].xyz, r12.xyz);
        r17.z = dot(cb1[r5.z+8].xyz, r12.xyz);
        r8.w = dot(r17.xyz, r17.xyz);
        r14.x = dot(r16.xyz, r17.xyz);
        r15.w = dot(r16.xyz, r16.xyz);
        r15.w = -cb1[r5.z+5].x * cb1[r5.z+5].x + r15.w;
        r15.w = r15.w * r8.w;
        r15.w = r14.x * r14.x + -r15.w;
        r15.w = sqrt(r15.w);
        r14.x = r15.w + -r14.x;
        r8.w = r14.x / r8.w;
        r17.xyz = r17.xyz * r8.www + r16.xyz;
        r19.xyz = cb1[r5.z+7].xyz * r17.yyy;
        r17.xyw = r17.xxx * cb1[r5.z+6].xyz + r19.xyz;
        r17.xyz = r17.zzz * cb1[r5.z+8].xyz + r17.xyw;
        r8.w = dot(r17.xyz, r17.xyz);
        r8.w = rsqrt(r8.w);
        r18.xyz = r17.xyz * r8.www;
      } else {
        if (r11.w != 0) {
          r17.x = dot(cb1[r5.z+6].xyz, r12.xyz);
          r17.y = dot(cb1[r5.z+7].xyz, r12.xyz);
          r17.z = dot(cb1[r5.z+8].xyz, r12.xyz);
          r19.xyz = cb1[r5.z+5].xyz + -r16.xyz;
          r19.xyz = r19.xyz / r17.xyz;
          r20.xyz = -cb1[r5.z+5].xyz + -r16.xyz;
          r20.xyz = r20.xyz / r17.xyz;
          r21.xyz = cmp(float3(0,0,0) < r17.xyz);
          r19.xyz = r21.xyz ? r19.xyz : r20.xyz;
          r8.w = min(r19.x, r19.y);
          r8.w = min(r8.w, r19.z);
          r16.xyz = r17.xyz * r8.www + r16.xyz;
          r17.xyz = cb1[r5.z+7].xyz * r16.yyy;
          r16.xyw = r16.xxx * cb1[r5.z+6].xyz + r17.xyz;
          r16.xyz = r16.zzz * cb1[r5.z+8].xyz + r16.xyw;
          r8.w = dot(r16.xyz, r16.xyz);
          r8.w = rsqrt(r8.w);
          r18.xyz = r16.xyz * r8.www;
        } else {
          r18.xyz = r12.xyz;
        }
      }
      r16.xyz = t3.SampleLevel(s2_s, r18.xyzw, r0.z).xyz;
      r13.xyz = r18.xyz;
      r8.w = dot(cb1[r5.z+4].xyzw, r13.xyzw);
      r8.w = max(0, r8.w);
      r8.w = max(9.99999975e-005, r8.w);
      r11.w = r1.z / r8.w;
      r11.w = min(1, r11.w);
      r11.w = r11.w * 2 + r1.z;
      r8.w = 2 + r8.w;
      r8.w = r11.w / r8.w;
      r13.xyz = r16.xyz * r7.www;
      r13.xyz = abs(cb1[r5.z+9].www) * r13.xyz;
      r5.z = -1 + r8.w;
      r5.z = r5.z * cb0[92].w + 1;
      r13.xyz = r13.xyz * r5.zzz + r15.xyz;
      r5.z = r7.w + r4.w;
      r7.w = 1 + r2.z;
      r3.w = r9.w;
    } else {
      r13.xyz = r15.xyz;
      r7.w = r2.z;
      r5.z = r4.w;
    }
    r8.w = cmp(r5.z >= 1);
    r9.w = cmp(r7.w >= 2);
    r8.w = (int)r8.w | (int)r9.w;
    if (r8.w != 0) {
      r15.xyz = r13.xyz;
      r2.z = r7.w;
      r4.w = r5.z;
      break;
    }
    r15.xyz = r13.xyz;
    r2.z = r7.w;
    r4.w = r5.z;
  }
  r1.y = cmp(r2.z < 2);
  r2.x = cmp(r4.w < 1);
  r1.y = r1.y ? r2.x : 0;
  if (r1.y != 0) {
    r12.w = 0;
    r13.xyz = t3.SampleLevel(s2_s, r12.xyzw, r0.z).xyz;
    r12.w = 1;
    r0.z = dot(cb1[3].xyzw, r12.xyzw);
    r0.z = max(0, r0.z);
    r0.z = max(9.99999975e-005, r0.z);
    r1.y = r1.z / r0.z;
    r1.y = min(1, r1.y);
    r1.y = r1.y * 2 + r1.z;
    r0.z = 2 + r0.z;
    r0.z = r1.y / r0.z;
    r1.y = 1 + -r4.w;
    r12.xyz = r13.xyz * r1.yyy;
    r0.z = -1 + r0.z;
    r0.z = r0.z * cb0[92].w + 1;
    r15.xyz = r12.xyz * r0.zzz + r15.xyz;
    r4.w = 1;
  }
  r0.z = max(1, r4.w);
  r12.xyz = r15.xyz / r0.zzz;
  r12.xyz = cb0[92].zzz * r12.xyz;
  r12.xyz = cb0[91].yyy * r12.xyz;
  if (r5.w != 0) {
    r13.xyzw = t1.SampleBias(s1_s, v1.xy, cb0[88].x).xyzw;
    r0.z = 1 + -r13.w;
    r15.xyz = r12.xyz * r0.zzz;
    r12.xyz = r13.xyz * r13.www + r15.xyz;
  }
  r12.xyz = r12.xyz * r10.xxx;
  r10.xyz = r12.xyz * r10.yzw;
  r4.xyz = cb0[91].xxx * r4.xyz;
  r4.xyz = r4.xyz * r11.xyz;
  r4.xyz = r4.xyz * r14.yzw;
  r1.xyz = r4.xyz * r1.xxx;
  r1.xyz = r1.xyz * r2.yyy;
  r2.xyz = r3.xyz * r11.xyz + r10.xyz;
  r1.xyz = r1.xyz * float3(0.318309873,0.318309873,0.318309873) + r2.xyz;
  r1.xyz = r7.xyz + r1.xyz;
  r1.xyz = max(float3(0,0,0), r1.xyz);
  r1.xyz = min(float3(255,255,255), r1.xyz);
  r0.z = sqrt(r2.w);
  r0.z = r0.z * cb0[136].w + -cb0[134].w;
  r0.z = max(0, r0.z);
  r2.x = cb0[135].w + -cb0[133].w;
  r2.y = r6.y * 0.00100000005 + -cb0[133].w;
  r2.z = dot(-r9.xyz, cb0[136].xyz);
  r3.xyz = cb0[134].xyz + cb0[132].xyz;
  r4.xyz = cb0[133].xyz + r3.xyz;
  r2.x = r2.x + -r2.y;
  r2.x = r2.x / cb0[131].w;
  r2.x = max(0.00999999978, r2.x);
  r2.w = -1.44269502 * r2.x;
  r2.w = exp2(r2.w);
  r2.w = 1 + -r2.w;
  r2.x = r2.w / r2.x;
  r2.y = -r2.y / cb0[131].w;
  r2.y = 1.44269502 * r2.y;
  r2.y = exp2(r2.y);
  r2.x = r2.x * r2.y;
  r0.z = r2.x * -r0.z;
  r2.xyw = r0.zzz * r4.xyz;
  r2.xyw = float3(1.44269502,1.44269502,1.44269502) * r2.xyw;
  r2.xyw = exp2(r2.xyw);
  r0.z = r2.z * r2.z + 1;
  r0.z = 0.0596831031 * r0.z;
  r3.w = cb0[132].w * cb0[132].w + 1;
  r4.w = cb0[132].w + cb0[132].w;
  r2.z = -r4.w * r2.z + r3.w;
  r3.w = -cb0[132].w * cb0[132].w + 1;
  r4.w = 12.566371 * r2.z;
  r2.z = sqrt(r2.z);
  r2.z = r4.w * r2.z;
  r2.z = r3.w / r2.z;
  r7.xyz = cb0[132].xyz * r2.zzz;
  r7.xyz = cb0[134].xyz * r0.zzz + r7.xyz;
  r3.xyz = cb0[135].xyz * r3.xyz;
  r3.xyz = cb0[131].xyz * r7.xyz + r3.xyz;
  r3.xyz = r3.xyz / r4.xyz;
  r3.xyz = max(float3(0,0,0), r3.xyz);
  r3.xyz = min(float3(255,255,255), r3.xyz);
  r4.xyz = float3(1,1,1) + -r2.xyw;
  r3.xyz = r4.xyz * r3.xyz;
  r1.xyz = r1.xyz * r2.xyw + r3.xyz;
  r0.z = cmp(0 < cb0[141].z);
  if (r0.z != 0) {
    r0.z = abs(r1.w) * cb0[142].x + cb0[142].y;
    r0.z = log2(r0.z);
    r0.z = cb0[142].z * r0.z;
    r2.z = r0.z / cb0[141].z;
    r0.w = asint(cb0[88].w) & 7;
    r0.xyz = mad((int3)r0.xyw, int3(0,0,0), int3(0,0,0));
    r0.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r0.y = mad((int)r0.z, (int)r0.x, (int)r0.y);
    r0.z = mad((int)r0.x, (int)r0.y, (int)r0.z);
    r3.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r3.y = mad((int)r0.z, (int)r3.x, (int)r0.y);
    r0.xy = (uint2)r3.xy >> int2(16,16);
    r0.xy = (uint2)r0.xy;
    r0.xy = r0.xy * float2(3.05180438e-005,3.05180438e-005) + float2(-1,-1);
    r0.xy = cb0[145].ww * r0.xy + r5.xy;
    r2.xy = cb0[143].xy * r0.xy;
    r0.x = dot(-r9.xyz, -r8.xyz);
    r0.y = cmp(5.96046448e-008 < r0.x);
    r0.x = rcp(r0.x);
    r0.x = r0.y ? r0.x : 0;
    r0.x = cb0[141].w * r0.x;
    r0.yzw = -cb0[32].xyz + r6.xyz;
    r0.y = dot(r0.yzw, r0.yzw);
    r0.w = max(9.99999994e-009, r0.y);
    r0.w = rsqrt(r0.w);
    r2.w = r0.y * r0.w;
    r3.x = r0.x * r0.w;
    r3.y = r3.x * r0.z + cb0[32].y;
    r0.z = -r3.x * r0.z + r0.z;
    r0.x = -r0.x * r0.w + 1;
    r0.x = r0.x * r2.w;
    r2.w = -cb0[137].x + r3.y;
    r2.w = cb0[137].z * r2.w;
    r2.w = max(-127, r2.w);
    r2.w = exp2(-r2.w);
    r2.w = cb0[137].y * r2.w;
    r3.x = cb0[137].z * r0.z;
    r3.x = max(-127, r3.x);
    r3.z = exp2(-r3.x);
    r3.z = 1 + -r3.z;
    r3.z = r3.z / r3.x;
    r3.w = -r3.x * 0.240226507 + 0.693147182;
    r3.x = cmp(5.96046448e-008 < abs(r3.x));
    r3.x = r3.x ? r3.z : r3.w;
    r2.w = r3.x * r2.w;
    r3.x = cmp(0 < cb0[140].y);
    r3.y = -cb0[140].z + r3.y;
    r3.y = cb0[140].x * r3.y;
    r3.y = max(-127, r3.y);
    r3.y = exp2(-r3.y);
    r3.y = cb0[140].y * r3.y;
    r0.z = cb0[140].x * r0.z;
    r0.z = max(-127, r0.z);
    r3.z = exp2(-r0.z);
    r3.z = 1 + -r3.z;
    r3.z = r3.z / r0.z;
    r3.w = -r0.z * 0.240226507 + 0.693147182;
    r0.z = cmp(5.96046448e-008 < abs(r0.z));
    r0.z = r0.z ? r3.z : r3.w;
    r0.z = r3.y * r0.z + r2.w;
    r0.z = r3.x ? r0.z : r2.w;
    r0.x = r0.z * r0.x;
    r0.x = exp2(-r0.x);
    r0.x = min(1, r0.x);
    r0.x = max(cb0[139].w, r0.x);
    r0.z = -r0.y * r0.w + cb0[138].x;
    r0.y = r0.y * r0.w + -cb0[138].z;
    r0.yz = saturate(cb0[138].wy * r0.yz);
    r0.x = r0.x + r0.z;
    r0.x = r0.x + r0.y;
    r0.x = min(1, r0.x);
    r0.y = 1 + -r0.x;
    r0.yzw = cb0[139].xyz * r0.yyy;
    r2.xyzw = t10.SampleLevel(s1_s, r2.xyz, 0).xyzw;
    r1.w = -cb0[144].z + abs(r1.w);
    r1.w = saturate(1000000 * r1.w);
    r2.xyzw = float4(-0,-0,-0,-1) + r2.xyzw;
    r2.xyzw = r1.wwww * r2.xyzw + float4(0,0,0,1);
    r0.yzw = r0.yzw * r2.www + r2.xyz;
    r2.w = r2.w * r0.x;
  } else {
    r3.xyz = -cb0[32].xyz + r6.xyz;
    r0.x = dot(r3.xyz, r3.xyz);
    r1.w = max(9.99999994e-009, r0.x);
    r1.w = rsqrt(r1.w);
    r3.x = r1.w * r0.x;
    r3.z = -cb0[137].x + cb0[32].y;
    r3.z = cb0[137].z * r3.z;
    r3.z = max(-127, r3.z);
    r3.z = exp2(-r3.z);
    r3.zw = cb0[137].yz * r3.zy;
    r3.w = max(-127, r3.w);
    r4.x = exp2(-r3.w);
    r4.x = 1 + -r4.x;
    r4.x = r4.x / r3.w;
    r4.y = -r3.w * 0.240226507 + 0.693147182;
    r3.w = cmp(5.96046448e-008 < abs(r3.w));
    r3.w = r3.w ? r4.x : r4.y;
    r3.z = r3.z * r3.w;
    r3.w = cmp(0 < cb0[140].y);
    r4.x = -cb0[140].z + cb0[32].y;
    r4.x = cb0[140].x * r4.x;
    r4.x = max(-127, r4.x);
    r4.x = exp2(-r4.x);
    r4.x = cb0[140].y * r4.x;
    r3.y = cb0[140].x * r3.y;
    r3.y = max(-127, r3.y);
    r4.y = exp2(-r3.y);
    r4.y = 1 + -r4.y;
    r4.y = r4.y / r3.y;
    r4.z = -r3.y * 0.240226507 + 0.693147182;
    r3.y = cmp(5.96046448e-008 < abs(r3.y));
    r3.y = r3.y ? r4.y : r4.z;
    r3.y = r4.x * r3.y + r3.z;
    r3.y = r3.w ? r3.y : r3.z;
    r3.x = r3.y * r3.x;
    r3.x = exp2(-r3.x);
    r3.x = min(1, r3.x);
    r3.x = max(cb0[139].w, r3.x);
    r3.y = -r0.x * r1.w + cb0[138].x;
    r3.y = saturate(cb0[138].y * r3.y);
    r0.x = r0.x * r1.w + -cb0[138].z;
    r0.x = saturate(cb0[138].w * r0.x);
    r1.w = r3.x + r3.y;
    r0.x = r1.w + r0.x;
    r2.w = min(1, r0.x);
    r0.x = 1 + -r2.w;
    r0.yzw = cb0[139].xyz * r0.xxx;
  }
  r2.xyz = r1.xyz * r2.www + r0.yzw;
  o0.xyzw = r2.xyzw;
  o1.xyzw = float4(0,0,0,0);
  return;
}