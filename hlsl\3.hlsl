// ---- Created with 3Dmigoto v1.2.52 on Sun Aug 10 16:08:38 2025
Texture2D<float4> t16 : register(t16);

Texture2D<float4> t15 : register(t15);

Texture2D<float4> t14 : register(t14);

Texture3D<float4> t13 : register(t13);

Texture3D<float4> t12 : register(t12);

Texture3D<float4> t11 : register(t11);

Texture3D<float4> t10 : register(t10);

Texture2D<float4> t9 : register(t9);

Texture2D<float4> t8 : register(t8);

Texture2D<float4> t7 : register(t7);

Texture2D<float4> t6 : register(t6);

Texture2D<float4> t5 : register(t5);

TextureCubeArray<float4> t3 : register(t3);

Texture2D<float4> t2 : register(t2);

Texture2D<float4> t1 : register(t1);

Texture2D<float4> t0 : register(t0);

SamplerState s4_s : register(s4);

SamplerComparisonState s3_s : register(s3);

SamplerState s2_s : register(s2);

SamplerState s1_s : register(s1);

SamplerState s0_s : register(s0);

cbuffer cb4 : register(b4)
{
  float4 cb4[369];
}

cbuffer cb3 : register(b3)
{
  float4 cb3[2054];
}

cbuffer cb2 : register(b2)
{
  float4 cb2[3];
}

cbuffer cb1 : register(b1)
{
  float4 cb1[196];
}

cbuffer cb0 : register(b0)
{
  float4 cb0[181];
}




// 3Dmigoto declarations
#define cmp -
Texture1D<float4> IniParams : register(t120);
Texture2D<float4> StereoParams : register(t125);


void main( 
  float4 v0 : SV_POSITION0,
  float2 v1 : TEXCOORD0,
  out float4 o0 : SV_Target0,
  out float4 o1 : SV_Target1)
{
  const float4 icb[] = { { 1.000000, 0, 0, 0},
                              { 0, 1.000000, 0, 0},
                              { 0, 0, 1.000000, 0},
                              { 0, 0, 0, 1.000000} };
// Needs manual fix for instruction: 
// unknown dcl_: dcl_resource_structured t4, 4 
  float4 r0,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10,r11,r12,r13,r14,r15,r16,r17,r18,r19,r20,r21,r22,r23,r24,r25,r26,r27,r28,r29,r30,r31,r32,r33;
  uint4 bitmask, uiDest;
  float4 fDest;

  float4 x0[8];
  r0.xy = (uint2)v0.xy;
  r0.z = 0;
  r1.xyzw = t14.Load(r0.xyz).xyzw;
  r2.xyzw = t15.Load(r0.xyz).xyzw;
  r3.xyzw = t16.Load(r0.xyz).xyzw;
  r1.w = 3 * r1.w;
  r1.w = round(r1.w);
  r1.w = (uint)r1.w;
  r1.w = dot(float3(0.5,0.800000012,1), icb[r1.w+0].yzw);
  r4.xy = r2.xy * float2(2,2) + float2(-1,-1);
  r4.z = dot(float2(1,1), abs(r4.xy));
  r5.y = 1 + -r4.z;
  r4.z = cmp(r5.y < 0);
  r6.xy = float2(1,1) + -abs(r4.yx);
  r6.zw = cmp(r4.xy >= float2(0,0));
  r6.zw = r6.zw ? float2(1,1) : float2(-1,-1);
  r6.xy = r6.xy * r6.zw;
  r5.xz = r4.zz ? r6.xy : r4.xy;
  r4.x = dot(r5.xyz, r5.xyz);
  r4.x = rsqrt(r4.x);
  r4.xyz = r5.xyz * r4.xxx;
  r5.xy = (uint2)r0.xy;
  r5.zw = float2(0.5,0.5) + r5.xy;
  r5.zw = cb0[62].zw * r5.zw;
  r5.z = t0.SampleLevel(s0_s, r5.zw, 0).x;
  r6.xy = cb0[62].zw * v0.xy;
  r6.zw = r6.xy * float2(2,2) + float2(-1,-1);
  r7.xyzw = cb0[21].xyzw * -r6.wwww;
  r7.xyzw = cb0[20].xyzw * r6.zzzz + r7.xyzw;
  r7.xyzw = cb0[22].xyzw * r5.zzzz + r7.xyzw;
  r7.xyzw = cb0[23].xyzw + r7.xyzw;
  r7.xyz = r7.xyz / r7.www;
  r5.z = cb0[1].z * r7.y;
  r5.z = cb0[0].z * r7.x + r5.z;
  r5.z = cb0[2].z * r7.z + r5.z;
  r5.z = cb0[3].z + r5.z;
  r5.w = cmp(cb0[66].w == 0.000000);
  r8.xyz = cb0[32].xyz + -r7.xyz;
  r9.x = cb0[0].z;
  r9.y = cb0[1].z;
  r9.z = cb0[2].z;
  r8.xyz = r5.www ? r8.xyz : r9.xyz;
  r5.w = dot(r8.xyz, r8.xyz);
  r6.z = rsqrt(r5.w);
  r10.xyz = r8.xyz * r6.zzz;
  r6.w = dot(r4.xyz, r10.xyz);
  r6.w = max(0, r6.w);
  r11.xyzw = r2.zzzz * float4(-1,-0.0274999999,-0.572000027,0.0219999999) + float4(1,0.0425000004,1.03999996,-0.0399999991);
  r8.w = r11.x * r11.x;
  r9.w = -9.27999973 * r6.w;
  r9.w = exp2(r9.w);
  r8.w = min(r9.w, r8.w);
  r8.w = r8.w * r11.x + r11.y;
  r11.xy = r8.ww * float2(-1.03999996,1.03999996) + r11.zw;
  r8.w = r11.x * 0.0399999991 + r11.y;
  r6.x = t9.SampleLevel(s1_s, r6.xy, 0).x;
  r11.x = t7.Load(r0.xyz).x;
  r0.z = dot(-r10.xyz, r4.xyz);
  r0.z = r0.z + r0.z;
  r12.xyz = r4.xyz * -r0.zzz + -r10.xyz;
  r0.z = dot(-cb3[0].xyz, r12.xyz);
  r13.xyz = -r0.zzz * -cb3[0].xyz + r12.xyz;
  r0.z = cmp(r0.z < cb3[4].z);
  r6.y = dot(r13.xyz, r13.xyz);
  r6.y = rsqrt(r6.y);
  r13.xyz = r13.xyz * r6.yyy;
  r13.xyz = cb3[4].yyy * r13.xyz;
  r13.xyz = cb3[4].zzz * -cb3[0].xyz + r13.xyz;
  r6.y = dot(r13.xyz, r13.xyz);
  r6.y = rsqrt(r6.y);
  r13.xyz = r13.xyz * r6.yyy;
  r13.xyz = r0.zzz ? r13.xyz : r12.xyz;
  r0.z = dot(r13.xyz, r4.xyz);
  r14.xyz = r8.xyz * r6.zzz + r13.xyz;
  r6.y = dot(r14.xyz, r14.xyz);
  r6.y = rsqrt(r6.y);
  r14.xyz = r14.xyz * r6.yyy;
  r2.x = saturate(r0.z);
  r6.y = saturate(dot(r4.xyz, r14.xyz));
  r9.w = saturate(dot(r10.xyz, r14.xyz));
  r10.w = dot(r10.xyz, r13.xyz);
  r2.y = min(1, r6.w);
  r13.x = r2.z * r2.z;
  r13.y = r13.x * r13.x;
  r13.zw = -r2.yx * r13.yy + r2.yx;
  r13.zw = r13.zw * r2.yx + r13.yy;
  r13.zw = sqrt(r13.zw);
  r13.zw = r13.zw * r2.xy;
  r13.z = r13.z + r13.w;
  r13.z = 9.99999975e-005 + r13.z;
  r13.z = 0.5 / r13.z;
  r13.w = r6.y * r13.y + -r6.y;
  r6.y = r13.w * r6.y + 1;
  r6.y = r6.y * r6.y;
  r6.y = r13.y / r6.y;
  r9.w = 1 + -r9.w;
  r13.y = r9.w * r9.w;
  r13.y = r13.y * r13.y;
  r13.w = r13.y * r9.w;
  r9.w = -r13.y * r9.w + 1;
  r9.w = r9.w * 0.0399999991 + r13.w;
  r6.y = r6.y * r13.z;
  r6.y = r6.y * r9.w;
  r6.y = min(2048, r6.y);
  r14.xyzw = r2.yzxz * float4(0.96875,0.96875,0.96875,0.96875) + float4(0.015625,0.015625,0.015625,0.015625);
  r9.w = t8.SampleLevel(s4_s, r14.xy, 0).x;
  r13.y = t8.SampleLevel(s4_s, r14.zw, 0).x;
  r13.z = 1 + -r2.z;
  r13.w = -r13.z * 0.383026004 + -0.0761947036;
  r13.w = r13.z * r13.w + 1.04997003;
  r13.z = r13.z * r13.w + 0.409254998;
  r13.z = min(0.999000013, r13.z);
  r13.w = 1 + -r13.z;
  r13.y = r13.y * r9.w;
  r13.y = r13.y * r13.z;
  r13.y = r13.y / r13.w;
  r13.y = 0.0073469379 * r13.y;
  r14.x = -r13.w * 0.0857142806 + 1;
  r13.y = r13.y / r14.x;
  r6.y = r13.y + r6.y;
  r14.yzw = float3(1,0.666666687,0.333333343) + r1.zzz;
  r14.yzw = frac(r14.yzw);
  r14.yzw = r14.yzw * float3(6,6,6) + float3(-3,-3,-3);
  r14.yzw = saturate(float3(-1,-1,-1) + abs(r14.yzw));
  r14.yzw = float3(-1,-1,-1) + r14.yzw;
  r14.yzw = r1.www * r14.yzw + float3(1,1,1);
  r14.yzw = r14.yzw * r3.www;
  r1.x = max(0.00999999978, r1.x);
  r10.w = saturate(-r10.w);
  r1.x = 12 * r1.x;
  r1.z = log2(r10.w);
  r1.z = r1.x * r1.z;
  r1.z = exp2(r1.z);
  r1.w = 1 + -r3.w;
  r3.w = r1.w * -2.9000001 + 3;
  r1.z = r3.w * r1.z;
  r0.z = saturate(r0.z * 0.666666687 + 0.333333343);
  r0.z = log2(r0.z);
  r0.z = 1.5 * r0.z;
  r0.z = exp2(r0.z);
  r0.z = r0.z * 1.66666663 + -1;
  r0.z = r1.w * r0.z + 1;
  r0.z = r1.y * r0.z;
  r10.w = 0.159154937 * r0.z;
  r0.z = -r0.z * 0.159154937 + 1;
  r0.z = r1.z * r0.z + r10.w;
  r15.xyz = r14.yzw * r0.zzz;
  r0.z = cb3[4].x * r6.y;
  r0.z = max(0, r0.z);
  r0.z = min(1000, r0.z);
  r11.yw = float2(0.5,1);
  r16.xyz = t6.SampleBias(s1_s, r11.xy, cb0[88].x).xyz;
  r17.xyz = r3.xyz + r0.zzz;
  r15.xyz = r17.xyz * r2.xxx + r15.xyz;
  r15.xyz = cb3[1].xyz * r15.xyz;
  r0.z = 1 + -r11.x;
  r16.xyz = r15.xyz * r16.xyz + -r15.xyz;
  r15.xyz = r0.zzz * r16.xyz + r15.xyz;
  r11.xy = (uint2)r0.xy >> int2(5,5);
  r0.z = mad((int)r11.y, asint(cb2[0].w), (int)r11.x);
  r1.z = (uint)r0.z << 3;
  r2.x = -cb0[65].y * cb2[2].w + abs(r5.z);
  r2.x = (int)r2.x;
  r6.y = (int)r2.x + asint(-cb2[1].y);
  r6.y = (int)r6.y + 1;
  r6.y = max(0, (int)r6.y);
  r6.y = min(1, (int)r6.y);
  r10.w = asint(cb2[1].y) + -1;
  r2.x = min((int)r10.w, (int)r2.x);
  r2.x = (uint)r2.x << 3;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r16.x, r1.z, l(0), t4.xxxx
r16.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.x = (((uint)r0.z << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.y = (((uint)r0.z << 3) & bitmask.y) | ((uint)2 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.z = (((uint)r0.z << 3) & bitmask.z) | ((uint)3 & ~bitmask.z);
  bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.w = (((uint)r0.z << 3) & bitmask.w) | ((uint)4 & ~bitmask.w);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r16.y, r17.x, l(0), t4.xxxx
r16.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r16.z, r17.y, l(0), t4.xxxx
r16.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r16.w, r17.z, l(0), t4.xxxx
r16.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.x, r17.w, l(0), t4.xxxx
r17.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.x = (((uint)r0.z << 3) & bitmask.x) | ((uint)5 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.y = (((uint)r0.z << 3) & bitmask.y) | ((uint)6 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.z = (((uint)r0.z << 3) & bitmask.z) | ((uint)7 & ~bitmask.z);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.y, r18.x, l(0), t4.xxxx
r17.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.z, r18.y, l(0), t4.xxxx
r17.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.w, r18.z, l(0), t4.xxxx
r17.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r0.z = (int)r2.x + asint(cb0[90].y);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r0.z, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r2.x = (int)-r6.y + 1;
  r18.x = (int)r1.z * (int)r2.x;
  r19.xyzw = (int4)r0.zzzz + int4(1,2,3,4);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r19.x, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r18.y = (int)r2.x * (int)r1.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r19.y, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r18.z = (int)r2.x * (int)r1.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r19.z, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r18.w = (int)r2.x * (int)r1.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.z, r19.w, l(0), t4.xxxx
r1.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.x = (int)r2.x * (int)r1.z;
  r20.xyz = (int3)r0.zzz + int3(5,6,7);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r20.x, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.y = (int)r2.x * (int)r0.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r20.y, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.z = (int)r2.x * (int)r0.z;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r0.z, r20.z, l(0), t4.xxxx
r0.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.w = (int)r2.x * (int)r0.z;
  r16.xyzw = (int4)r16.xyzw & (int4)r18.xyzw;
  r17.xyzw = (int4)r17.xyzw & (int4)r19.xyzw;
  x0[0].x = r16.x;
  x0[1].x = r16.y;
  x0[2].x = r16.z;
  x0[3].x = r16.w;
  x0[4].x = r17.x;
  x0[5].x = r17.y;
  x0[6].x = r17.z;
  x0[7].x = r17.w;
  r16.w = 1;
  r17.w = 1;
  r18.z = r2.z;
  r19.xyz = float3(0,0,0);
  r0.z = 1;
  r1.z = 0;
  while (true) {
    r2.x = cmp(7 < (uint)r1.z);
    if (r2.x != 0) break;
    r2.x = x0[r1.z+0].x;
    r6.y = (uint)r1.z << 5;
    r20.xyz = r19.xyz;
    r10.w = r0.z;
    r11.x = r2.x;
    while (true) {
      if (r11.x == 0) break;
      r11.y = firstbitlow((uint)r11.x);
      r13.y = (int)r6.y + (int)r11.y;
      r11.y = 1 << (int)r11.y;
      r11.y = (int)r11.y ^ (int)r11.x;
      bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r21.x = (((uint)r13.y << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r21.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)5 & ~bitmask.y);
      bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r21.z = (((uint)r13.y << 3) & bitmask.z) | ((uint)6 & ~bitmask.z);
      bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r21.w = (((uint)r13.y << 3) & bitmask.w) | ((uint)7 & ~bitmask.w);
      r15.w = (uint)cb3[r21.y+6].w;
      r15.w = cmp((int)r15.w == 1);
      if (r15.w != 0) {
        r22.xyz = asuint(cb3[r21.y+6].xyz) >> int3(16,16,16);
        r23.xyz = f16tof32(cb3[r21.y+6].xyz);
        r22.xyz = f16tof32(r22.xzy);
        r24.xyz = asuint(cb3[r21.z+6].xyz) >> int3(16,16,16);
        r25.xyz = f16tof32(cb3[r21.z+6].xyz);
        r24.xyw = f16tof32(r24.xyz);
        r16.xyz = -cb3[r21.x+6].xyz + r7.xyz;
        r26.xz = r23.xy;
        r26.yw = r22.xz;
        r15.w = dot(r16.xyzw, r26.xyzw);
        r22.x = r23.z;
        r22.z = r25.x;
        r22.w = r24.x;
        r18.w = dot(r16.xyzw, r22.xyzw);
        r24.xz = r25.yz;
        r16.x = dot(r16.xyzw, r24.xyzw);
        r15.w = max(abs(r18.w), abs(r15.w));
        r15.w = max(r15.w, abs(r16.x));
        r16.x = cmp(1 < r15.w);
        if (r16.x != 0) {
          r11.x = r11.y;
          continue;
        }
        r16.x = cb3[r21.w+6].x * 0.5 + 0.5;
        r15.w = -r16.x + r15.w;
        r16.x = 1 + -r16.x;
        r15.w = saturate(r15.w / r16.x);
        r15.w = 1 + -r15.w;
        r15.w = r15.w * r15.w;
      } else {
        r15.w = 1;
      }
      r16.x = (uint)r13.y << 3;
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r16.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)2 & ~bitmask.y);
      bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r16.z = (((uint)r13.y << 3) & bitmask.z) | ((uint)3 & ~bitmask.z);
      r13.y = (uint)cb3[r16.x+6].w;
      r18.w = cmp((uint)r13.y < 2);
      if (r18.w != 0) {
        r18.w = cmp(0.5 >= cb3[r16.z+6].z);
        if (r18.w != 0) {
          r18.w = cb3[r16.y+6].y * 0.5 + 0.5;
          r22.x = -abs(cb3[r16.y+6].x) + r18.w;
          r22.y = cb3[r16.y+6].y + -r22.x;
          r18.w = 1 + -abs(r22.x);
          r18.w = r18.w + -abs(r22.y);
          r18.w = max(5.96046448e-008, r18.w);
          r19.w = cmp(cb3[r16.y+6].x >= 0);
          r22.z = r19.w ? r18.w : -r18.w;
          r18.w = dot(r22.xyz, r22.xyz);
          r18.w = rsqrt(r18.w);
          r22.xyz = r22.xyz * r18.www;
          r23.xyz = cb3[r21.x+6].xyz + -r7.xyz;
          r18.w = dot(r23.xyz, r23.xyz);
          r19.w = rsqrt(r18.w);
          r17.xyz = r23.xyz * r19.www;
          r24.xyz = cb3[r16.y+6].zzz * r22.xyz;
          r25.xyz = -r24.xyz * float3(0.5,0.5,0.5) + r23.xyz;
          r26.xyz = r24.xyz * float3(0.5,0.5,0.5) + r23.xyz;
          r20.w = (int)r13.y & 1;
          r21.y = cmp((int)r20.w != 0);
          r22.w = cmp(0 < cb3[r16.y+6].z);
          r21.y = r21.y ? r22.w : 0;
          r11.z = saturate(dot(r4.xyz, r17.xyz));
          r22.w = dot(r25.xyz, r25.xyz);
          r22.w = sqrt(r22.w);
          r23.w = dot(r26.xyz, r26.xyz);
          r23.w = sqrt(r23.w);
          r24.w = dot(r25.xyz, r26.xyz);
          r24.w = r22.w * r23.w + r24.w;
          r24.w = r24.w * 0.5 + 1;
          r27.y = rcp(r24.w);
          r24.w = dot(r4.xyz, r25.xyz);
          r22.w = r24.w / r22.w;
          r24.w = dot(r4.xyz, r26.xyz);
          r23.w = r24.w / r23.w;
          r22.w = r23.w + r22.w;
          r27.x = saturate(0.5 * r22.w);
          r18.xy = r21.yy ? r27.xy : r11.zw;
          r11.z = cmp(cb3[r21.z+6].w < 0);
          r22.w = 1 + r18.w;
          r22.w = 1 / r22.w;
          r23.w = r21.y ? 1.000000 : 0;
          r24.w = -r22.w + r18.y;
          r22.w = r23.w * r24.w + r22.w;
          r23.w = cb3[r21.x+6].w * cb3[r21.x+6].w;
          r18.w = r23.w * r18.w;
          r18.w = -r18.w * r18.w + 1;
          r18.w = max(0, r18.w);
          r18.w = r18.w * r18.w;
          r18.w = r22.w * r18.w;
          r23.xyz = cb3[r21.x+6].www * r23.xyz;
          r22.w = dot(r23.xyz, r23.xyz);
          r22.w = min(1, r22.w);
          r22.w = 1 + -r22.w;
          r22.w = log2(r22.w);
          r22.w = cb3[r21.z+6].w * r22.w;
          r22.w = exp2(r22.w);
          r18.y = r22.w * r18.y;
          r11.z = r11.z ? r18.w : r18.y;
          r18.y = dot(r17.xyz, -r22.xyz);
          r18.y = -cb3[r16.y+6].z + r18.y;
          r18.y = saturate(cb3[r16.y+6].w * r18.y);
          r18.y = r18.y * r18.y;
          r18.y = r18.y * r11.z;
          r11.z = r20.w ? r11.z : r18.y;
          r18.y = cmp(0 < r11.z);
          if (r18.y != 0) {
            r18.y = (int)cb3[r16.z+6].x;
            r22.xyz = -cb3[r21.x+6].xyz + r7.xyz;
            r23.xyz = cmp(abs(r22.yzz) < abs(r22.xxy));
            r18.w = r23.y ? r23.x : 0;
            r23.xyw = cmp(float3(0,0,0) < r22.xyz);
            r22.w = asuint(cb3[r16.y+6].w) >> 24;
            if (8 == 0) r26.x = 0; else if (8+16 < 32) {             r26.x = (uint)cb3[r16.y+6].w << (32-(8 + 16)); r26.x = (uint)r26.x >> (32-8);            } else r26.x = (uint)cb3[r16.y+6].w >> 16;
            if (8 == 0) r26.y = 0; else if (8+8 < 32) {             r26.y = (uint)cb3[r16.y+6].w << (32-(8 + 8)); r26.y = (uint)r26.y >> (32-8);            } else r26.y = (uint)cb3[r16.y+6].w >> 8;
            r22.w = r23.x ? r22.w : r26.x;
            r23.x = 255 & asint(cb3[r16.y+6].w);
            r23.x = r23.y ? r26.y : r23.x;
            if (8 == 0) r23.y = 0; else if (8+8 < 32) {             r23.y = (uint)cb3[r16.z+6].x << (32-(8 + 8)); r23.y = (uint)r23.y >> (32-8);            } else r23.y = (uint)cb3[r16.z+6].x >> 8;
            r24.w = 255 & asint(cb3[r16.z+6].x);
            r23.y = r23.w ? r23.y : r24.w;
            r23.x = r23.z ? r23.x : r23.y;
            r18.w = r18.w ? r22.w : r23.x;
            r22.w = cmp((int)r18.w < 80);
            r18.w = r22.w ? r18.w : -1;
            r18.y = r20.w ? r18.w : r18.y;
            r18.w = cmp((int)r18.y >= 0);
            r20.w = dot(r22.xyz, r22.xyz);
            r20.w = max(1.17549435e-038, r20.w);
            r20.w = rsqrt(r20.w);
            r22.xyz = r22.xyz * r20.www;
            r20.w = dot(r4.xyz, r22.xyz);
            r20.w = max(0, r20.w);
            r20.w = min(0.899999976, r20.w);
            r20.w = 1 + -r20.w;
            r23.xy = cb4[r18.y+256].xy * r20.ww;
            r20.w = 5 * r23.y;
            r22.xyz = -r22.xyz * r23.xxx + r7.xyz;
            r22.xyz = r4.xyz * r20.www + r22.xyz;
            r20.w = (uint)r18.y << 2;
            r23.xyzw = cb4[r20.w+33].xyzw * r22.yyyy;
            r23.xyzw = cb4[r20.w+32].xyzw * r22.xxxx + r23.xyzw;
            r22.xyzw = cb4[r20.w+34].xyzw * r22.zzzz + r23.xyzw;
            r22.xyzw = cb4[r20.w+35].xyzw + r22.xyzw;
            r22.xyz = r22.xyz / r22.www;
            r23.xy = cb4[r18.y+312].zw + -cb4[r18.y+312].xy;
            r23.xy = r22.xy * r23.xy + cb4[r18.y+312].xy;
            r26.xyz = cmp(float3(0,0,0) >= r22.xyz);
            r22.xyw = cmp(r22.xyz >= float3(1,1,1));
            r22.xyw = (int3)r22.xyw | (int3)r26.xyz;
            r20.w = (int)r22.y | (int)r22.x;
            r20.w = (int)r22.w | (int)r20.w;
            r22.x = (int)r22.z & 0x7fffffff;
            r22.x = cmp(0x7f800000 < (uint)r22.x);
            r20.w = (int)r20.w | (int)r22.x;
            r22.xy = r23.xy * cb4[368].zw + float2(0.5,0.5);
            r22.xy = floor(r22.xy);
            r23.xy = r23.xy * cb4[368].zw + -r22.xy;
            r26.xyzw = float4(0.5,1,0.5,1) + r23.xxyy;
            r27.xyzw = r26.xxzz * r26.xxzz;
            r23.zw = float2(0.0799999982,0.0799999982) * r27.yw;
            r26.xz = r27.xz * float2(0.5,0.5) + -r23.xy;
            r27.xy = float2(1,1) + -r23.xy;
            r27.zw = min(float2(0,0), r23.xy);
            r27.zw = -r27.zw * r27.zw + r27.xy;
            r23.xy = max(float2(0,0), r23.xy);
            r23.xy = -r23.xy * r23.xy + r26.yw;
            r27.zw = float2(1,1) + r27.zw;
            r23.xy = float2(1,1) + r23.xy;
            r28.xy = float2(0.159999996,0.159999996) * r26.xz;
            r29.xy = float2(0.159999996,0.159999996) * r27.xy;
            r27.xy = float2(0.159999996,0.159999996) * r27.zw;
            r30.xy = float2(0.159999996,0.159999996) * r23.xy;
            r23.xy = float2(0.159999996,0.159999996) * r26.yw;
            r28.z = r27.x;
            r28.w = r23.x;
            r29.z = r30.x;
            r29.w = r23.z;
            r26.xyzw = r29.zwxz + r28.zwxz;
            r27.z = r28.y;
            r27.w = r23.y;
            r30.z = r29.y;
            r30.w = r23.w;
            r23.xyz = r30.zyw + r27.zyw;
            r27.xyz = r29.xzw / r26.zwy;
            r27.xyz = float3(-2.5,-0.5,1.5) + r27.xyz;
            r28.xyz = r30.zyw / r23.xyz;
            r28.xyz = float3(-2.5,-0.5,1.5) + r28.xyz;
            r27.xyz = cb4[368].xxx * r27.yxz;
            r28.xyz = cb4[368].yyy * r28.xyz;
            r27.w = r28.x;
            r29.xyzw = r22.xyxy * cb4[368].xyxy + r27.ywxw;
            r30.xy = r22.xy * cb4[368].xy + r27.zw;
            r28.w = r27.y;
            r27.yw = r28.yz;
            r31.xyzw = r22.xyxy * cb4[368].xyxy + r27.xyzy;
            r28.xyzw = r22.xyxy * cb4[368].xyxy + r28.wywz;
            r27.xyzw = r22.xyxy * cb4[368].xyxy + r27.xwzw;
            r32.xyzw = r26.zwyz * r23.xxxy;
            r33.xyzw = r26.xyzw * r23.yyzz;
            r22.x = r26.y * r23.z;
            r22.y = t5.SampleCmpLevelZero(s3_s, r29.xy, r22.z).x;
            r22.w = t5.SampleCmpLevelZero(s3_s, r29.zw, r22.z).x;
            r22.w = r32.y * r22.w;
            r22.y = r32.x * r22.y + r22.w;
            r22.w = t5.SampleCmpLevelZero(s3_s, r30.xy, r22.z).x;
            r22.y = r32.z * r22.w + r22.y;
            r22.w = t5.SampleCmpLevelZero(s3_s, r28.xy, r22.z).x;
            r22.y = r32.w * r22.w + r22.y;
            r22.w = t5.SampleCmpLevelZero(s3_s, r31.xy, r22.z).x;
            r22.y = r33.x * r22.w + r22.y;
            r22.w = t5.SampleCmpLevelZero(s3_s, r31.zw, r22.z).x;
            r22.y = r33.y * r22.w + r22.y;
            r22.w = t5.SampleCmpLevelZero(s3_s, r28.zw, r22.z).x;
            r22.y = r33.z * r22.w + r22.y;
            r22.w = t5.SampleCmpLevelZero(s3_s, r27.xy, r22.z).x;
            r22.y = r33.w * r22.w + r22.y;
            r22.z = t5.SampleCmpLevelZero(s3_s, r27.zw, r22.z).x;
            r22.x = r22.x * r22.z + r22.y;
            r22.x = -1 + r22.x;
            r18.y = cb4[r18.y+256].w * r22.x + 1;
            r18.y = r20.w ? 1 : r18.y;
            r18.y = r18.w ? r18.y : 1;
            r18.w = saturate(cb3[r21.w+6].y * r19.w);
            r19.w = saturate(cb3[r16.y+6].z * r19.w);
            r19.w = r19.w * 0.5 + r13.x;
            r19.w = min(1, r19.w);
            r22.w = r13.x / r19.w;
            r19.w = dot(r12.xyz, r24.xyz);
            r23.xyz = r19.www * r12.xyz + -r24.xyz;
            r20.w = dot(r25.xyz, r23.xyz);
            r19.w = r19.w * r19.w;
            r19.w = cb3[r16.y+6].z * cb3[r16.y+6].z + -r19.w;
            r19.w = saturate(r20.w / r19.w);
            r23.xyz = r19.www * r24.xyz + r25.xyz;
            r19.w = dot(r23.xyz, r23.xyz);
            r19.w = rsqrt(r19.w);
            r22.xyz = r23.xyz * r19.www;
            r22.xyzw = r21.yyyy ? r22.xyzw : r17.xyzw;
            r17.xyz = r8.xyz * r6.zzz + r22.xyz;
            r19.w = dot(r17.xyz, r17.xyz);
            r19.w = rsqrt(r19.w);
            r17.xyz = r19.www * r17.xyz;
            r19.w = dot(r4.xyz, r22.xyz);
            r20.w = saturate(dot(r4.xyz, r17.xyz));
            r17.x = saturate(dot(r10.xyz, r17.xyz));
            r17.y = dot(r10.xyz, r22.xyz);
            r17.z = cmp(0 < r18.w);
            r18.w = r18.w * r18.w;
            r21.y = r17.x * 3.5999999 + 0.400000006;
            r18.w = r18.w / r21.y;
            r18.w = r2.z * r2.z + r18.w;
            r18.w = min(1, r18.w);
            r17.z = r17.z ? r18.w : r13.x;
            r17.z = r17.z * r17.z;
            r18.w = -r2.y * r17.z + r2.y;
            r18.w = r18.w * r2.y + r17.z;
            r18.w = sqrt(r18.w);
            r21.y = -r18.x * r17.z + r18.x;
            r21.y = r21.y * r18.x + r17.z;
            r21.y = sqrt(r21.y);
            r21.y = r21.y * r2.y;
            r18.w = r18.x * r18.w + r21.y;
            r18.w = 9.99999975e-005 + r18.w;
            r18.w = 0.5 / r18.w;
            r21.y = r20.w * r17.z + -r20.w;
            r20.w = r21.y * r20.w + 1;
            r20.w = r20.w * r20.w;
            r17.z = r17.z / r20.w;
            r17.z = r17.z * r22.w;
            r17.x = 1 + -r17.x;
            r20.w = r17.x * r17.x;
            r20.w = r20.w * r20.w;
            r21.y = r20.w * r17.x;
            r17.x = -r20.w * r17.x + 1;
            r17.x = r17.x * 0.0399999991 + r21.y;
            r17.z = r17.z * r18.w;
            r17.x = r17.z * r17.x;
            r17.x = min(2048, r17.x);
            r22.xy = r18.xz * float2(0.96875,0.96875) + float2(0.015625,0.015625);
            r17.z = t8.SampleLevel(s4_s, r22.xy, 0).x;
            r17.z = r17.z * r9.w;
            r17.z = r17.z * r13.z;
            r17.z = r17.z / r13.w;
            r17.z = 0.0073469379 * r17.z;
            r17.z = r17.z / r14.x;
            r17.x = r17.x + r17.z;
            r17.y = saturate(-r17.y);
            r17.y = log2(r17.y);
            r17.y = r17.y * r1.x;
            r17.y = exp2(r17.y);
            r17.y = r17.y * r3.w;
            r17.z = saturate(r19.w * 0.666666687 + 0.333333343);
            r17.z = log2(r17.z);
            r17.z = 1.5 * r17.z;
            r17.z = exp2(r17.z);
            r17.z = r17.z * 1.66666663 + -1;
            r17.z = r1.w * r17.z + 1;
            r17.z = r17.z * r1.y;
            r18.w = 0.159154937 * r17.z;
            r17.z = -r17.z * 0.159154937 + 1;
            r17.y = r17.y * r17.z + r18.w;
            r22.xyz = r17.yyy * r14.yzw;
            r17.x = cb3[r21.w+6].z * r17.x;
            r17.x = max(0, r17.x);
            r17.x = min(1000, r17.x);
            r23.xyz = cb3[r16.x+6].xyz * r11.zzz;
            r23.xyz = r23.xyz * r18.yyy;
            r23.xyz = r23.xyz * r15.www;
            r17.xyz = r17.xxx + r3.xyz;
            r17.xyz = r17.xyz * r18.xxx + r22.xyz;
            r17.xyz = r23.xyz * r17.xyz;
          } else {
            r17.xyz = float3(0,0,0);
          }
        } else {
          r17.xyz = float3(0,0,0);
        }
        r20.xyz = r20.xyz + r17.xyz;
      } else {
        r11.z = cmp(0.5 >= cb3[r16.z+6].z);
        if (r11.z != 0) {
          r11.z = cb3[r16.y+6].y * 0.5 + 0.5;
          r17.x = -abs(cb3[r16.y+6].x) + r11.z;
          r17.y = cb3[r16.y+6].y + -r17.x;
          r11.z = 1 + -abs(r17.x);
          r11.z = r11.z + -abs(r17.y);
          r11.z = max(5.96046448e-008, r11.z);
          r15.w = cmp(cb3[r16.y+6].x >= 0);
          r17.z = r15.w ? r11.z : -r11.z;
          r11.z = dot(r17.xyz, r17.xyz);
          r11.z = rsqrt(r11.z);
          r17.xyz = r17.xyz * r11.zzz;
          r18.xyw = cb3[r21.x+6].xyz + -r7.xyz;
          r11.z = dot(r18.xyw, r18.xyw);
          r15.w = rsqrt(r11.z);
          r22.xyz = r18.xyw * r15.www;
          r23.xyz = cb3[r16.y+6].zzz * r17.xyz;
          r24.xyz = -r23.xyz * float3(0.5,0.5,0.5) + r18.xyw;
          r23.xyz = r23.xyz * float3(0.5,0.5,0.5) + r18.xyw;
          r13.y = (int)r13.y & 1;
          r15.w = cmp((int)r13.y != 0);
          r16.x = cmp(0 < cb3[r16.y+6].z);
          r15.w = r15.w ? r16.x : 0;
          r16.x = dot(r24.xyz, r24.xyz);
          r16.x = sqrt(r16.x);
          r19.w = dot(r23.xyz, r23.xyz);
          r19.w = sqrt(r19.w);
          r20.w = dot(r24.xyz, r23.xyz);
          r16.x = r16.x * r19.w + r20.w;
          r16.x = r16.x * 0.5 + 1;
          r16.x = rcp(r16.x);
          r16.x = r15.w ? r16.x : 1;
          r19.w = cmp(cb3[r21.z+6].w < 0);
          r20.w = 1 + r11.z;
          r20.w = 1 / r20.w;
          r15.w = r15.w ? 1.000000 : 0;
          r21.y = -r20.w + r16.x;
          r15.w = r15.w * r21.y + r20.w;
          r20.w = cb3[r21.x+6].w * cb3[r21.x+6].w;
          r11.z = r20.w * r11.z;
          r11.z = -r11.z * r11.z + 1;
          r11.z = max(0, r11.z);
          r11.z = r11.z * r11.z;
          r11.z = r15.w * r11.z;
          r18.xyw = cb3[r21.x+6].www * r18.xyw;
          r15.w = dot(r18.xyw, r18.xyw);
          r15.w = min(1, r15.w);
          r15.w = 1 + -r15.w;
          r15.w = log2(r15.w);
          r15.w = cb3[r21.z+6].w * r15.w;
          r15.w = exp2(r15.w);
          r15.w = r16.x * r15.w;
          r11.z = r19.w ? r11.z : r15.w;
          r15.w = dot(r22.xyz, -r17.xyz);
          r15.w = -cb3[r16.y+6].z + r15.w;
          r15.w = saturate(cb3[r16.y+6].w * r15.w);
          r15.w = r15.w * r15.w;
          r15.w = r15.w * r11.z;
          r11.z = r13.y ? r11.z : r15.w;
          r11.z = cmp(0 < r11.z);
          if (r11.z != 0) {
            r11.z = (int)cb3[r16.z+6].x;
            r17.xyz = -cb3[r21.x+6].xyz + r7.xyz;
            r18.xyw = cmp(abs(r17.yzz) < abs(r17.xxy));
            r15.w = r18.y ? r18.x : 0;
            r21.xyz = cmp(float3(0,0,0) < r17.xyz);
            r16.x = asuint(cb3[r16.y+6].w) >> 24;
            if (8 == 0) r18.x = 0; else if (8+16 < 32) {             r18.x = (uint)cb3[r16.y+6].w << (32-(8 + 16)); r18.x = (uint)r18.x >> (32-8);            } else r18.x = (uint)cb3[r16.y+6].w >> 16;
            if (8 == 0) r18.y = 0; else if (8+8 < 32) {             r18.y = (uint)cb3[r16.y+6].w << (32-(8 + 8)); r18.y = (uint)r18.y >> (32-8);            } else r18.y = (uint)cb3[r16.y+6].w >> 8;
            r16.x = r21.x ? r16.x : r18.x;
            r16.y = 255 & asint(cb3[r16.y+6].w);
            if (8 == 0) r18.x = 0; else if (8+8 < 32) {             r18.x = (uint)cb3[r16.z+6].x << (32-(8 + 8)); r18.x = (uint)r18.x >> (32-8);            } else r18.x = (uint)cb3[r16.z+6].x >> 8;
            r16.z = 255 & asint(cb3[r16.z+6].x);
            r16.yz = r21.yz ? r18.yx : r16.yz;
            r16.y = r18.w ? r16.y : r16.z;
            r15.w = r15.w ? r16.x : r16.y;
            r16.x = cmp((int)r15.w < 80);
            r15.w = r16.x ? r15.w : -1;
            r11.z = r13.y ? r15.w : r11.z;
            r13.y = cmp((int)r11.z >= 0);
            r15.w = dot(r17.xyz, r17.xyz);
            r15.w = max(1.17549435e-038, r15.w);
            r15.w = rsqrt(r15.w);
            r16.xyz = r17.xyz * r15.www;
            r15.w = dot(r4.xyz, r16.xyz);
            r15.w = max(0, r15.w);
            r15.w = min(0.899999976, r15.w);
            r15.w = 1 + -r15.w;
            r17.xy = cb4[r11.z+256].xy * r15.ww;
            r15.w = 5 * r17.y;
            r16.xyz = -r16.xyz * r17.xxx + r7.xyz;
            r16.xyz = r4.xyz * r15.www + r16.xyz;
            r15.w = (uint)r11.z << 2;
            r21.xyzw = cb4[r15.w+33].xyzw * r16.yyyy;
            r21.xyzw = cb4[r15.w+32].xyzw * r16.xxxx + r21.xyzw;
            r21.xyzw = cb4[r15.w+34].xyzw * r16.zzzz + r21.xyzw;
            r21.xyzw = cb4[r15.w+35].xyzw + r21.xyzw;
            r16.xyz = r21.xyz / r21.www;
            r17.xy = cb4[r11.z+312].zw + -cb4[r11.z+312].xy;
            r17.xy = r16.xy * r17.xy + cb4[r11.z+312].xy;
            r18.xyw = cmp(float3(0,0,0) >= r16.xyz);
            r21.xyz = cmp(r16.xyz >= float3(1,1,1));
            r18.xyw = (int3)r18.xyw | (int3)r21.xyz;
            r15.w = (int)r18.y | (int)r18.x;
            r15.w = (int)r18.w | (int)r15.w;
            r16.x = (int)r16.z & 0x7fffffff;
            r16.x = cmp(0x7f800000 < (uint)r16.x);
            r15.w = (int)r15.w | (int)r16.x;
            r16.xy = r17.xy * cb4[368].zw + float2(0.5,0.5);
            r16.xy = floor(r16.xy);
            r17.xy = r17.xy * cb4[368].zw + -r16.xy;
            r21.xyzw = float4(0.5,1,0.5,1) + r17.xxyy;
            r22.xyzw = r21.xxzz * r21.xxzz;
            r18.xy = float2(0.0799999982,0.0799999982) * r22.yw;
            r21.xz = r22.xz * float2(0.5,0.5) + -r17.xy;
            r22.xy = float2(1,1) + -r17.xy;
            r22.zw = min(float2(0,0), r17.xy);
            r22.zw = -r22.zw * r22.zw + r22.xy;
            r17.xy = max(float2(0,0), r17.xy);
            r17.xy = -r17.xy * r17.xy + r21.yw;
            r22.zw = float2(1,1) + r22.zw;
            r17.xy = float2(1,1) + r17.xy;
            r23.xy = float2(0.159999996,0.159999996) * r21.xz;
            r24.xy = float2(0.159999996,0.159999996) * r22.xy;
            r22.xy = float2(0.159999996,0.159999996) * r22.zw;
            r25.xy = float2(0.159999996,0.159999996) * r17.xy;
            r17.xy = float2(0.159999996,0.159999996) * r21.yw;
            r23.z = r22.x;
            r23.w = r17.x;
            r24.z = r25.x;
            r24.w = r18.x;
            r21.xyzw = r24.zwxz + r23.zwxz;
            r22.z = r23.y;
            r22.w = r17.y;
            r25.z = r24.y;
            r25.w = r18.y;
            r17.xyz = r25.zyw + r22.zyw;
            r18.xyw = r24.xzw / r21.zwy;
            r18.xyw = float3(-2.5,-0.5,1.5) + r18.xyw;
            r22.xyz = r25.zyw / r17.xyz;
            r22.xyz = float3(-2.5,-0.5,1.5) + r22.xyz;
            r23.xyz = cb4[368].xxx * r18.yxw;
            r22.xyz = cb4[368].yyy * r22.xyz;
            r23.w = r22.x;
            r24.xyzw = r16.xyxy * cb4[368].xyxy + r23.ywxw;
            r18.xy = r16.xy * cb4[368].xy + r23.zw;
            r22.w = r23.y;
            r23.yw = r22.yz;
            r25.xyzw = r16.xyxy * cb4[368].xyxy + r23.xyzy;
            r22.xyzw = r16.xyxy * cb4[368].xyxy + r22.wywz;
            r23.xyzw = r16.xyxy * cb4[368].xyxy + r23.xwzw;
            r26.xyzw = r21.zwyz * r17.xxxy;
            r27.xyzw = r21.xyzw * r17.yyzz;
            r16.x = r21.y * r17.z;
            r16.y = t5.SampleCmpLevelZero(s3_s, r24.xy, r16.z).x;
            r17.x = t5.SampleCmpLevelZero(s3_s, r24.zw, r16.z).x;
            r17.x = r26.y * r17.x;
            r16.y = r26.x * r16.y + r17.x;
            r17.x = t5.SampleCmpLevelZero(s3_s, r18.xy, r16.z).x;
            r16.y = r26.z * r17.x + r16.y;
            r17.x = t5.SampleCmpLevelZero(s3_s, r22.xy, r16.z).x;
            r16.y = r26.w * r17.x + r16.y;
            r17.x = t5.SampleCmpLevelZero(s3_s, r25.xy, r16.z).x;
            r16.y = r27.x * r17.x + r16.y;
            r17.x = t5.SampleCmpLevelZero(s3_s, r25.zw, r16.z).x;
            r16.y = r27.y * r17.x + r16.y;
            r17.x = t5.SampleCmpLevelZero(s3_s, r22.zw, r16.z).x;
            r16.y = r27.z * r17.x + r16.y;
            r17.x = t5.SampleCmpLevelZero(s3_s, r23.xy, r16.z).x;
            r16.y = r27.w * r17.x + r16.y;
            r16.z = t5.SampleCmpLevelZero(s3_s, r23.zw, r16.z).x;
            r16.x = r16.x * r16.z + r16.y;
            r16.x = -1 + r16.x;
            r11.z = cb4[r11.z+256].w * r16.x + 1;
            r11.z = r15.w ? 1 : r11.z;
            r11.z = r13.y ? r11.z : 1;
          } else {
            r11.z = 1;
          }
        } else {
          r11.z = 1;
        }
        r10.w = r11.z * r10.w;
      }
      r11.x = r11.y;
    }
    r19.xyz = r20.xyz;
    r0.z = r10.w;
    r1.z = (int)r1.z + 1;
  }
  r1.xzw = r19.xyz * r0.zzz;
  r1.xzw = r15.xyz * r6.xxx + r1.xzw;
  r2.xy = cmp(cb0[92].xy != float2(0,0));
  if (r2.x != 0) {
    r0.z = t2.SampleBias(s0_s, v1.xy, cb0[88].x).x;
    r0.z = min(r0.z, r1.y);
    r2.x = r6.w + r0.z;
    r3.w = r2.z * -16 + -1;
    r3.w = exp2(r3.w);
    r2.x = log2(abs(r2.x));
    r2.x = r3.w * r2.x;
    r2.x = exp2(r2.x);
    r2.x = -1 + r2.x;
    r2.x = r2.x + r0.z;
    r6.xyz = saturate(r2.xxx);
    r8.xyz = r3.xyz * float3(2.04040003,2.04040003,2.04040003) + float3(-0.332399994,-0.332399994,-0.332399994);
    r11.xyz = r3.xyz * float3(-4.79510021,-4.79510021,-4.79510021) + float3(0.641700029,0.641700029,0.641700029);
    r13.xyz = r3.xyz * float3(2.75519991,2.75519991,2.75519991) + float3(0.690299988,0.690299988,0.690299988);
    r8.xyz = r0.zzz * r8.xyz + r11.xyz;
    r8.xyz = r8.xyz * r0.zzz + r13.xyz;
    r8.xyz = r8.xyz * r0.zzz;
    r8.xyz = max(r8.xyz, r0.zzz);
  } else {
    r6.xyz = r1.yyy;
    r8.xyz = r1.yyy;
  }
  r4.w = 1;
  r11.x = dot(cb0[178].xyzw, r4.xyzw);
  r11.y = dot(cb0[179].xyzw, r4.xyzw);
  r11.z = dot(cb0[180].xyzw, r4.xyzw);
  r13.xyz = r4.xyz * float3(0.25,0.25,0.25) + r7.xyz;
  r0.z = 64 * cb0[177].x;
  r0.z = cmp(r0.z >= -r0.z);
  r15.xy = r0.zz ? float2(64,0.015625) : float2(-64,-0.015625);
  r0.z = cb0[177].x * r15.y;
  r0.z = frac(r0.z);
  r0.z = r15.x * r0.z;
  r0.z = trunc(r0.z);
  r15.xy = r0.zz * float2(2.08299994,4.8670001) + r5.xy;
  r0.z = dot(r15.xy, float2(0.0671105608,0.00583714992));
  r0.z = frac(r0.z);
  r0.z = 52.9829178 * r0.z;
  r0.z = frac(r0.z);
  r0.z = r0.z * 2 + -1;
  r13.xyz = r0.zzz * float3(0.200000003,0.200000003,0.200000003) + r13.xyz;
  r15.xyz = -cb0[175].xyz + r13.xyz;
  r0.z = max(abs(r15.x), abs(r15.y));
  r0.z = max(r0.z, abs(r15.z));
  r1.y = -896 + r0.z;
  r1.y = saturate(0.015625 * r1.y);
  r2.x = cmp(0 < cb0[175].w);
  r3.w = cmp(r1.y < 1);
  r2.x = r2.x ? r3.w : 0;
  if (r2.x != 0) {
    r15.xy = float2(-100,-200) + r0.zz;
    r15.xw = saturate(float2(0.0625,0.0833333358) * r15.yx);
    r16.xy = cmp(r15.wx < float2(1,1));
    r17.xyz = float3(0.001953125,0.00048828125,1);
    r17.w = r15.x;
    r17.xyzw = r16.yyyy ? r17.xyzw : float4(0.00048828125,0.00048828125,2,0);
    r15.xyz = float3(0.00390625,0.001953125,0);
    r15.xyzw = r16.xxxx ? r15.xyzw : r17.xyzw;
    r16.xyz = r15.xxx * r13.xyz;
    r16.xyz = frac(r16.xyz);
    r16.xyzw = t11.SampleLevel(s0_s, r16.xyz, r15.z).xyzw;
    r16.xyzw = r16.xyzw * float4(255,255,255,255) + float4(0.5,0.5,0.5,0.5);
    r16.xyzw = floor(r16.xyzw);
    r0.z = cmp(0 < r16.w);
    if (r0.z != 0) {
      r17.xyz = r13.xyz / r16.www;
      r17.xyz = frac(r17.xyz);
      r17.xyz = r17.xyz * float3(4,4,4) + float3(0.5,0.5,0.5);
      r16.xyz = r16.xyz * float3(5,5,5) + r17.xyz;
      r16.xyz = cb0[176].xyz * r16.xyz;
      r17.xyz = t12.SampleLevel(s1_s, r16.xyz, 0).xyz;
      r16.w = 0.333333343 * r16.z;
      r18.xyz = t13.SampleLevel(s1_s, r16.xyw, 0).xyz;
      r19.xyz = r16.xyz * float3(1,1,0.333333343) + float3(0,0,0.333333343);
      r19.xyz = t13.SampleLevel(s1_s, r19.xyz, 0).xyz;
      r16.xyz = r16.xyz * float3(1,1,0.333333343) + float3(0,0,0.666666687);
      r16.xyz = t13.SampleLevel(s1_s, r16.xyz, 0).xyz;
      r18.xyz = r18.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r18.xyz, r4.xyz);
      r0.z = r17.x * r0.z + r17.x;
      r18.x = max(0, r0.z);
      r19.xyz = r19.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r19.xyz, r4.xyz);
      r0.z = r17.y * r0.z + r17.y;
      r18.y = max(0, r0.z);
      r16.xyz = r16.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r16.xyz, r4.xyz);
      r0.z = r17.z * r0.z + r17.z;
      r18.z = max(0, r0.z);
    } else {
      r18.xyz = r11.xyz;
    }
    r0.z = cmp(0 < r15.w);
    if (r0.z != 0) {
      r16.xyz = r15.yyy * r13.xyz;
      r16.xyz = frac(r16.xyz);
      r0.z = 1 + r15.z;
      r16.xyzw = t11.SampleLevel(s0_s, r16.xyz, r0.z).xyzw;
      r16.xyzw = r16.xyzw * float4(255,255,255,255) + float4(0.5,0.5,0.5,0.5);
      r16.xyzw = floor(r16.xyzw);
      r0.z = cmp(0 < r16.w);
      if (r0.z != 0) {
        r13.xyz = r13.xyz / r16.www;
        r13.xyz = frac(r13.xyz);
        r13.xyz = r13.xyz * float3(4,4,4) + float3(0.5,0.5,0.5);
        r13.xyz = r16.xyz * float3(5,5,5) + r13.xyz;
        r13.xyz = cb0[176].xyz * r13.xyz;
        r15.xyz = t12.SampleLevel(s1_s, r13.xyz, 0).xyz;
        r13.w = 0.333333343 * r13.z;
        r16.xyz = t13.SampleLevel(s1_s, r13.xyw, 0).xyz;
        r17.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.333333343);
        r17.xyz = t13.SampleLevel(s1_s, r17.xyz, 0).xyz;
        r13.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.666666687);
        r13.xyz = t13.SampleLevel(s1_s, r13.xyz, 0).xyz;
        r16.xyz = r16.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r16.xyz, r4.xyz);
        r0.z = r15.x * r0.z + r15.x;
        r16.x = max(0, r0.z);
        r17.xyz = r17.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r17.xyz, r4.xyz);
        r0.z = r15.y * r0.z + r15.y;
        r16.y = max(0, r0.z);
        r13.xyz = r13.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r13.xyz, r4.xyz);
        r0.z = r15.z * r0.z + r15.z;
        r16.z = max(0, r0.z);
        r4.xyz = r16.xyz + -r18.xyz;
        r18.xyz = r15.www * r4.xyz + r18.xyz;
      }
    }
    r4.xyz = -r18.xyz + r11.xyz;
    r11.xyz = r1.yyy * r4.xyz + r18.xyz;
  } else {
    r4.xyz = r11.xyz;
  }
  r4.xyz = r2.xxx ? r11.xyz : r4.xyz;
  r3.xyz = r14.yzw * r2.www + r3.xyz;
  r3.xyz = r4.xyz * r3.xyz;
  r3.xyz = cb0[91].xxx * r3.xyz;
  r0.z = max(0.00100000005, r2.z);
  r0.z = log2(r0.z);
  r0.z = -r0.z * 1.20000005 + 1;
  r0.z = 6 + -r0.z;
  r2.xz = (uint2)cb1[0].wx;
  uiDest.xw = (uint2)r0.xy / (uint2)r2.xx;
  r2.xw = uiDest.xw;
  r1.y = mad((int)r2.w, (int)r2.z, (int)r2.x);
  r2.xz = (uint2)cb1[1].xy;
  r2.z = (uint)r2.z;
  r2.z = cb1[2].y / r2.z;
  r2.z = abs(r5.z) + -r2.z;
  r2.z = (int)r2.z;
  r2.w = (int)-r2.x + (int)r2.z;
  r2.xw = (int2)r2.xw + int2(-1,1);
  r2.w = min(1, (uint)r2.w);
  r2.x = min((uint)r2.x, (uint)r2.z);
  r1.y = (int)r1.y + asint(cb0[90].z);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.y, r1.y, l(0), t4.xxxx
r1.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r2.x = (int)r2.x + asint(cb0[90].w);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r2.x, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r2.x = (int)r2.w * (int)r2.x;
  r1.y = (int)r1.y & (int)r2.x;
  r2.x = dot(r4.xyz, float3(0.212672904,0.715152204,0.0721750036));
  r7.w = 1;
  r4.w = 1;
  r11.xyz = float3(0,0,0);
  r2.z = r1.y;
  r2.w = 0;
  r3.w = -1;
  r6.w = 0;
  while (true) {
    if (r2.z == 0) break;
    r9.w = firstbitlow((uint)r2.z);
    r10.w = 1 << (int)r9.w;
    r2.z = (int)r2.z ^ (int)r10.w;
    r9.w = (int)r9.w * 6;
    r13.x = dot(cb1[r9.w+6].xyzw, r7.xyzw);
    r13.y = dot(cb1[r9.w+7].xyzw, r7.xyzw);
    r13.z = dot(cb1[r9.w+8].xyzw, r7.xyzw);
    r14.xyz = r13.xyz / cb1[r9.w+5].xyz;
    r10.w = dot(abs(r14.xyz), abs(r14.xyz));
    r10.w = sqrt(r10.w);
    r11.w = cmp(cb1[r9.w+9].w < 0);
    r13.w = cmp(r10.w < 1);
    r14.w = max(abs(r14.y), abs(r14.z));
    r14.w = max(abs(r14.x), r14.w);
    r14.w = cmp(r14.w < 1);
    r13.w = r11.w ? r13.w : r14.w;
    if (r13.w != 0) {
      r13.w = f16tof32(cb1[r9.w+5].w);
      r14.w = asuint(cb1[r9.w+5].w) >> 16;
      r14.w = f16tof32(r14.w);
      r15.w = abs(r14.w);
      r14.w = cmp(0 < r14.w);
      r10.w = -cb1[r9.w+9].x + r10.w;
      r14.xyz = -cb1[r9.w+9].xyz + abs(r14.xyz);
      r14.xyz = r11.www ? r10.www : r14.xyz;
      r16.xyz = -cb1[r9.w+9].xyz + float3(1,1,1);
      r14.xyz = saturate(r14.xyz / r16.xyz);
      r14.xyz = float3(1,1,1) + -r14.xyz;
      r10.w = min(r14.y, r14.z);
      r10.w = min(r14.x, r10.w);
      r10.w = r11.w ? r14.x : r10.w;
      r14.x = cmp(r13.w < r3.w);
      r14.y = 1 + -r6.w;
      r10.w = r14.x ? r14.y : r10.w;
      if (r11.w != 0) {
        r14.x = dot(cb1[r9.w+6].xyz, r12.xyz);
        r14.y = dot(cb1[r9.w+7].xyz, r12.xyz);
        r14.z = dot(cb1[r9.w+8].xyz, r12.xyz);
        r11.w = dot(r14.xyz, r14.xyz);
        r16.x = dot(r13.xyz, r14.xyz);
        r16.y = dot(r13.xyz, r13.xyz);
        r16.y = -cb1[r9.w+5].x * cb1[r9.w+5].x + r16.y;
        r16.y = r16.y * r11.w;
        r16.y = r16.x * r16.x + -r16.y;
        r16.y = sqrt(r16.y);
        r16.x = r16.y + -r16.x;
        r11.w = r16.x / r11.w;
        r14.xyz = r14.xyz * r11.www + r13.xyz;
        r16.xyz = cb1[r9.w+7].xyz * r14.yyy;
        r16.xyz = r14.xxx * cb1[r9.w+6].xyz + r16.xyz;
        r14.xyz = r14.zzz * cb1[r9.w+8].xyz + r16.xyz;
        r11.w = dot(r14.xyz, r14.xyz);
        r11.w = rsqrt(r11.w);
        r15.xyz = r14.xyz * r11.www;
      } else {
        if (r14.w != 0) {
          r14.x = dot(cb1[r9.w+6].xyz, r12.xyz);
          r14.y = dot(cb1[r9.w+7].xyz, r12.xyz);
          r14.z = dot(cb1[r9.w+8].xyz, r12.xyz);
          r16.xyz = cb1[r9.w+5].xyz + -r13.xyz;
          r16.xyz = r16.xyz / r14.xyz;
          r17.xyz = -cb1[r9.w+5].xyz + -r13.xyz;
          r17.xyz = r17.xyz / r14.xyz;
          r18.xyz = cmp(float3(0,0,0) < r14.xyz);
          r16.xyz = r18.xyz ? r16.xyz : r17.xyz;
          r11.w = min(r16.x, r16.y);
          r11.w = min(r11.w, r16.z);
          r13.xyz = r14.xyz * r11.www + r13.xyz;
          r14.xyz = cb1[r9.w+7].xyz * r13.yyy;
          r14.xyz = r13.xxx * cb1[r9.w+6].xyz + r14.xyz;
          r13.xyz = r13.zzz * cb1[r9.w+8].xyz + r14.xyz;
          r11.w = dot(r13.xyz, r13.xyz);
          r11.w = rsqrt(r11.w);
          r15.xyz = r13.xyz * r11.www;
        } else {
          r15.xyz = r12.xyz;
        }
      }
      r13.xyz = t3.SampleLevel(s2_s, r15.xyzw, r0.z).xyz;
      r4.xyz = r15.xyz;
      r4.x = dot(cb1[r9.w+4].xyzw, r4.xyzw);
      r4.x = max(0, r4.x);
      r4.x = max(9.99999975e-005, r4.x);
      r4.y = r2.x / r4.x;
      r4.y = min(1, abs(r4.y));
      r4.y = r4.y * 2 + r2.x;
      r4.x = 2 + r4.x;
      r4.x = r4.y / r4.x;
      r13.xyz = r13.xyz * r10.www;
      r13.xyz = abs(cb1[r9.w+9].www) * r13.xyz;
      r4.x = -1 + r4.x;
      r4.x = r4.x * cb0[92].w + 1;
      r4.xyz = r13.xyz * r4.xxx + r11.xyz;
      r9.w = r10.w + r6.w;
      r10.w = 1 + r2.w;
      r3.w = r13.w;
    } else {
      r4.xyz = r11.xyz;
      r10.w = r2.w;
      r9.w = r6.w;
    }
    r11.w = cmp(r9.w >= 1);
    r13.x = cmp(r10.w >= 2);
    r11.w = (int)r11.w | (int)r13.x;
    if (r11.w != 0) {
      r11.xyz = r4.xyz;
      r2.w = r10.w;
      r6.w = r9.w;
      break;
    }
    r11.xyz = r4.xyz;
    r2.w = r10.w;
    r6.w = r9.w;
  }
  r1.y = cmp(r2.w < 2);
  r2.z = cmp(r6.w < 1);
  r1.y = r1.y ? r2.z : 0;
  if (r1.y != 0) {
    r12.w = 0;
    r4.xyz = t3.SampleLevel(s2_s, r12.xyzw, r0.z).xyz;
    r12.w = 1;
    r0.z = dot(cb1[3].xyzw, r12.xyzw);
    r0.z = max(0, r0.z);
    r0.z = max(9.99999975e-005, r0.z);
    r1.y = r2.x / r0.z;
    r1.y = min(1, abs(r1.y));
    r1.y = r1.y * 2 + r2.x;
    r0.z = 2 + r0.z;
    r0.z = r1.y / r0.z;
    r1.y = 1 + -r6.w;
    r2.xzw = r4.xyz * r1.yyy;
    r0.z = -1 + r0.z;
    r0.z = r0.z * cb0[92].w + 1;
    r11.xyz = r2.xzw * r0.zzz + r11.xyz;
    r6.w = 1;
  }
  r0.z = max(1, r6.w);
  r2.xzw = r11.xyz / r0.zzz;
  r2.xzw = cb0[92].zzz * r2.xzw;
  r2.xzw = cb0[91].yyy * r2.xzw;
  if (r2.y != 0) {
    r4.xyzw = t1.SampleBias(s1_s, v1.xy, cb0[88].x).xyzw;
    r0.z = 1 + -r4.w;
    r11.xyz = r2.xzw * r0.zzz;
    r2.xzw = r4.xyz * r4.www + r11.xyz;
  }
  r2.xyz = r2.xzw * r8.www;
  r2.xyz = r2.xyz * r6.xyz;
  r2.xyz = r3.xyz * r8.xyz + r2.xyz;
  r1.xyz = r2.xyz + r1.xzw;
  r1.xyz = max(float3(0,0,0), r1.xyz);
  r1.xyz = min(float3(255,255,255), r1.xyz);
  r0.z = sqrt(r5.w);
  r0.z = r0.z * cb0[136].w + -cb0[134].w;
  r0.z = max(0, r0.z);
  r1.w = cb0[135].w + -cb0[133].w;
  r2.x = r7.y * 0.00100000005 + -cb0[133].w;
  r2.y = dot(-r10.xyz, cb0[136].xyz);
  r3.xyz = cb0[134].xyz + cb0[132].xyz;
  r4.xyz = cb0[133].xyz + r3.xyz;
  r1.w = -r2.x + r1.w;
  r1.w = r1.w / cb0[131].w;
  r1.w = max(0.00999999978, r1.w);
  r2.z = -1.44269502 * r1.w;
  r2.z = exp2(r2.z);
  r2.z = 1 + -r2.z;
  r1.w = r2.z / r1.w;
  r2.x = -r2.x / cb0[131].w;
  r2.x = 1.44269502 * r2.x;
  r2.x = exp2(r2.x);
  r1.w = r2.x * r1.w;
  r0.z = r1.w * -r0.z;
  r2.xzw = r0.zzz * r4.xyz;
  r2.xzw = float3(1.44269502,1.44269502,1.44269502) * r2.xzw;
  r2.xzw = exp2(r2.xzw);
  r0.z = r2.y * r2.y + 1;
  r0.z = 0.0596831031 * r0.z;
  r1.w = cb0[132].w * cb0[132].w + 1;
  r3.w = cb0[132].w + cb0[132].w;
  r1.w = -r3.w * r2.y + r1.w;
  r2.y = -cb0[132].w * cb0[132].w + 1;
  r3.w = 12.566371 * r1.w;
  r1.w = sqrt(r1.w);
  r1.w = r3.w * r1.w;
  r1.w = r2.y / r1.w;
  r6.xyz = cb0[132].xyz * r1.www;
  r6.xyz = cb0[134].xyz * r0.zzz + r6.xyz;
  r3.xyz = cb0[135].xyz * r3.xyz;
  r3.xyz = cb0[131].xyz * r6.xyz + r3.xyz;
  r3.xyz = r3.xyz / r4.xyz;
  r3.xyz = max(float3(0,0,0), r3.xyz);
  r3.xyz = min(float3(255,255,255), r3.xyz);
  r4.xyz = float3(1,1,1) + -r2.xzw;
  r3.xyz = r4.xyz * r3.xyz;
  r1.xyz = r1.xyz * r2.xzw + r3.xyz;
  r0.z = cmp(0 < cb0[141].z);
  if (r0.z != 0) {
    r0.z = abs(r5.z) * cb0[142].x + cb0[142].y;
    r0.z = log2(r0.z);
    r0.z = cb0[142].z * r0.z;
    r2.z = r0.z / cb0[141].z;
    r0.w = asint(cb0[88].w) & 7;
    r0.xyz = mad((int3)r0.xyw, int3(0,0,0), int3(0,0,0));
    r0.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r0.y = mad((int)r0.z, (int)r0.x, (int)r0.y);
    r0.z = mad((int)r0.x, (int)r0.y, (int)r0.z);
    r3.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r3.y = mad((int)r0.z, (int)r3.x, (int)r0.y);
    r0.xy = (uint2)r3.xy >> int2(16,16);
    r0.xy = (uint2)r0.xy;
    r0.xy = r0.xy * float2(3.05180438e-005,3.05180438e-005) + float2(-1,-1);
    r0.xy = cb0[145].ww * r0.xy + r5.xy;
    r2.xy = cb0[143].xy * r0.xy;
    r0.x = dot(-r10.xyz, -r9.xyz);
    r0.y = cmp(5.96046448e-008 < r0.x);
    r0.x = rcp(r0.x);
    r0.x = r0.y ? r0.x : 0;
    r0.x = cb0[141].w * r0.x;
    r0.yzw = -cb0[32].xyz + r7.xyz;
    r0.y = dot(r0.yzw, r0.yzw);
    r0.w = max(9.99999994e-009, r0.y);
    r0.w = rsqrt(r0.w);
    r1.w = r0.y * r0.w;
    r2.w = r0.x * r0.w;
    r3.x = r2.w * r0.z + cb0[32].y;
    r0.z = -r2.w * r0.z + r0.z;
    r0.x = -r0.x * r0.w + 1;
    r0.x = r0.x * r1.w;
    r1.w = -cb0[137].x + r3.x;
    r1.w = cb0[137].z * r1.w;
    r1.w = max(-127, r1.w);
    r1.w = exp2(-r1.w);
    r1.w = cb0[137].y * r1.w;
    r2.w = cb0[137].z * r0.z;
    r2.w = max(-127, r2.w);
    r3.y = exp2(-r2.w);
    r3.y = 1 + -r3.y;
    r3.y = r3.y / r2.w;
    r3.z = -r2.w * 0.240226507 + 0.693147182;
    r2.w = cmp(5.96046448e-008 < abs(r2.w));
    r2.w = r2.w ? r3.y : r3.z;
    r1.w = r2.w * r1.w;
    r2.w = cmp(0 < cb0[140].y);
    r3.x = -cb0[140].z + r3.x;
    r3.x = cb0[140].x * r3.x;
    r3.x = max(-127, r3.x);
    r3.x = exp2(-r3.x);
    r3.x = cb0[140].y * r3.x;
    r0.z = cb0[140].x * r0.z;
    r0.z = max(-127, r0.z);
    r3.y = exp2(-r0.z);
    r3.y = 1 + -r3.y;
    r3.y = r3.y / r0.z;
    r3.z = -r0.z * 0.240226507 + 0.693147182;
    r0.z = cmp(5.96046448e-008 < abs(r0.z));
    r0.z = r0.z ? r3.y : r3.z;
    r0.z = r3.x * r0.z + r1.w;
    r0.z = r2.w ? r0.z : r1.w;
    r0.x = r0.z * r0.x;
    r0.x = exp2(-r0.x);
    r0.x = min(1, r0.x);
    r0.x = max(cb0[139].w, r0.x);
    r0.z = -r0.y * r0.w + cb0[138].x;
    r0.y = r0.y * r0.w + -cb0[138].z;
    r0.yz = saturate(cb0[138].wy * r0.yz);
    r0.x = r0.x + r0.z;
    r0.x = r0.x + r0.y;
    r0.x = min(1, r0.x);
    r0.y = 1 + -r0.x;
    r0.yzw = cb0[139].xyz * r0.yyy;
    r2.xyzw = t10.SampleLevel(s1_s, r2.xyz, 0).xyzw;
    r1.w = -cb0[144].z + abs(r5.z);
    r1.w = saturate(1000000 * r1.w);
    r2.xyzw = float4(-0,-0,-0,-1) + r2.xyzw;
    r2.xyzw = r1.wwww * r2.xyzw + float4(0,0,0,1);
    r0.yzw = r0.yzw * r2.www + r2.xyz;
    r2.w = r2.w * r0.x;
  } else {
    r3.xyz = -cb0[32].xyz + r7.xyz;
    r0.x = dot(r3.xyz, r3.xyz);
    r1.w = max(9.99999994e-009, r0.x);
    r1.w = rsqrt(r1.w);
    r3.x = r1.w * r0.x;
    r3.z = -cb0[137].x + cb0[32].y;
    r3.z = cb0[137].z * r3.z;
    r3.z = max(-127, r3.z);
    r3.z = exp2(-r3.z);
    r3.zw = cb0[137].yz * r3.zy;
    r3.w = max(-127, r3.w);
    r4.x = exp2(-r3.w);
    r4.x = 1 + -r4.x;
    r4.x = r4.x / r3.w;
    r4.y = -r3.w * 0.240226507 + 0.693147182;
    r3.w = cmp(5.96046448e-008 < abs(r3.w));
    r3.w = r3.w ? r4.x : r4.y;
    r3.z = r3.z * r3.w;
    r3.w = cmp(0 < cb0[140].y);
    r4.x = -cb0[140].z + cb0[32].y;
    r4.x = cb0[140].x * r4.x;
    r4.x = max(-127, r4.x);
    r4.x = exp2(-r4.x);
    r4.x = cb0[140].y * r4.x;
    r3.y = cb0[140].x * r3.y;
    r3.y = max(-127, r3.y);
    r4.y = exp2(-r3.y);
    r4.y = 1 + -r4.y;
    r4.y = r4.y / r3.y;
    r4.z = -r3.y * 0.240226507 + 0.693147182;
    r3.y = cmp(5.96046448e-008 < abs(r3.y));
    r3.y = r3.y ? r4.y : r4.z;
    r3.y = r4.x * r3.y + r3.z;
    r3.y = r3.w ? r3.y : r3.z;
    r3.x = r3.y * r3.x;
    r3.x = exp2(-r3.x);
    r3.x = min(1, r3.x);
    r3.x = max(cb0[139].w, r3.x);
    r3.y = -r0.x * r1.w + cb0[138].x;
    r3.y = saturate(cb0[138].y * r3.y);
    r0.x = r0.x * r1.w + -cb0[138].z;
    r0.x = saturate(cb0[138].w * r0.x);
    r1.w = r3.x + r3.y;
    r0.x = r1.w + r0.x;
    r2.w = min(1, r0.x);
    r0.x = 1 + -r2.w;
    r0.yzw = cb0[139].xyz * r0.xxx;
  }
  r2.xyz = r1.xyz * r2.www + r0.yzw;
  o0.xyzw = r2.xyzw;
  o1.xyzw = float4(0,0,0,0);
  return;
}