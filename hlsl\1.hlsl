// ---- Created with 3Dmigoto v1.2.52 on Sun Aug 10 16:06:03 2025
Texture2D<float4> t16 : register(t16);

Texture2D<float4> t15 : register(t15);

Texture2D<float4> t14 : register(t14);

Texture3D<float4> t13 : register(t13);

Texture3D<float4> t12 : register(t12);

Texture3D<float4> t11 : register(t11);

Texture3D<float4> t10 : register(t10);

Texture2D<float4> t9 : register(t9);

Texture2D<float4> t8 : register(t8);

Texture2D<float4> t7 : register(t7);

Texture2D<float4> t6 : register(t6);

Texture2D<float4> t5 : register(t5);

TextureCubeArray<float4> t3 : register(t3);

Texture2D<float4> t2 : register(t2);

Texture2D<float4> t1 : register(t1);

Texture2D<float4> t0 : register(t0);

SamplerState s4_s : register(s4);

SamplerComparisonState s3_s : register(s3);

SamplerState s2_s : register(s2);

SamplerState s1_s : register(s1);

SamplerState s0_s : register(s0);

cbuffer cb4 : register(b4)
{
  float4 cb4[369];
}

cbuffer cb3 : register(b3)
{
  float4 cb3[2054];
}

cbuffer cb2 : register(b2)
{
  float4 cb2[3];
}

cbuffer cb1 : register(b1)
{
  float4 cb1[196];
}

cbuffer cb0 : register(b0)
{
  float4 cb0[181];
}




// 3Dmigoto declarations
#define cmp -
Texture1D<float4> IniParams : register(t120);
Texture2D<float4> StereoParams : register(t125);


void main( 
  float4 v0 : SV_POSITION0,
  float2 v1 : TEXCOORD0,
  out float4 o0 : SV_Target0,
  out float4 o1 : SV_Target1)
{
// Needs manual fix for instruction: 
// unknown dcl_: dcl_resource_structured t4, 4 
  float4 r0,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10,r11,r12,r13,r14,r15,r16,r17,r18,r19,r20,r21,r22,r23,r24,r25,r26,r27,r28,r29,r30,r31,r32,r33,r34;
  uint4 bitmask, uiDest;
  float4 fDest;

  float4 x0[8];
  r0.xy = (uint2)v0.xy;
  r0.z = 0;
  r1.xy = t14.Load(r0.xyz).xy;
  r2.xyz = t15.Load(r0.xyz).xyz;
  r3.xyz = t16.Load(r0.xyz).xyz;
  r1.zw = r2.xy * float2(2,2) + float2(-1,-1);
  r2.w = dot(float2(1,1), abs(r1.zw));
  r4.y = 1 + -r2.w;
  r2.w = cmp(r4.y < 0);
  r5.xy = float2(1,1) + -abs(r1.wz);
  r5.zw = cmp(r1.zw >= float2(0,0));
  r5.zw = r5.zw ? float2(1,1) : float2(-1,-1);
  r5.xy = r5.xy * r5.zw;
  r4.xz = r2.ww ? r5.xy : r1.zw;
  r1.z = dot(r4.xyz, r4.xyz);
  r1.z = rsqrt(r1.z);
  r4.xyz = r4.xyz * r1.zzz;
  r1.zw = (uint2)r0.xy;
  r5.xy = float2(0.5,0.5) + r1.zw;
  r5.xy = cb0[62].zw * r5.xy;
  r2.w = t0.SampleLevel(s0_s, r5.xy, 0).x;
  r5.xy = cb0[62].zw * v0.xy;
  r5.zw = r5.xy * float2(2,2) + float2(-1,-1);
  r6.xyzw = cb0[21].xyzw * -r5.wwww;
  r6.xyzw = cb0[20].xyzw * r5.zzzz + r6.xyzw;
  r6.xyzw = cb0[22].xyzw * r2.wwww + r6.xyzw;
  r6.xyzw = cb0[23].xyzw + r6.xyzw;
  r6.xyz = r6.xyz / r6.www;
  r2.w = cb0[1].z * r6.y;
  r2.w = cb0[0].z * r6.x + r2.w;
  r2.w = cb0[2].z * r6.z + r2.w;
  r2.w = cb0[3].z + r2.w;
  r3.w = cmp(cb0[66].w == 0.000000);
  r7.xyz = cb0[32].xyz + -r6.xyz;
  r8.x = cb0[0].z;
  r8.y = cb0[1].z;
  r8.z = cb0[2].z;
  r7.xyz = r3.www ? r7.xyz : r8.xyz;
  r3.w = dot(r7.xyz, r7.xyz);
  r5.z = rsqrt(r3.w);
  r9.xyz = r7.xyz * r5.zzz;
  r5.w = dot(r4.xyz, r9.xyz);
  r10.x = max(0, r5.w);
  r11.xyz = -r3.xyz * r1.xxx + r3.xyz;
  r5.w = -r1.x * 0.0399999991 + 0.0399999991;
  r3.xyz = r3.xyz * r1.xxx + r5.www;
  r12.y = r2.z * r2.z;
  r13.x = r10.x * r10.x;
  r13.z = r13.x * r10.x;
  r1.x = r12.y * r12.y;
  r12.z = r1.x * r12.y;
  r10.yzw = float3(0.0365463011,9.0632,0.990440011);
  r14.x = dot(float2(3.32707,1), r10.xy);
  r14.y = dot(float2(-9.04755974,1), r10.xz);
  r12.x = 1;
  r5.w = dot(r14.xy, r12.xy);
  r13.yw = float2(9.04401016,1);
  r14.x = dot(float3(3.59684992,-1.36772001,1), r13.xzw);
  r14.y = dot(float3(-16.3174,1,9.22949028), r13.xyz);
  r15.x = 5.56588984;
  r15.yz = r13.xz;
  r14.z = dot(float3(1,19.7886009,-20.2122993), r15.xyz);
  r7.w = dot(r14.xyz, r12.xyz);
  r5.w = r5.w / r7.w;
  r14.x = dot(float2(-1.28514004,1), r10.xw);
  r13.x = 1.29677999;
  r13.y = r10.x;
  r14.y = dot(float2(1,-0.755906999), r13.xy);
  r7.w = dot(r14.xy, r12.xy);
  r14.x = dot(float3(2.9233799,59.4188004,1), r13.yzw);
  r13.xw = float2(20.3225002,121.563004);
  r14.y = dot(float3(1,-27.0301991,222.591995), r13.xyz);
  r14.z = dot(float3(626.130005,316.627014,1), r13.yzw);
  r8.w = dot(r14.xyz, r12.xyz);
  r7.w = r7.w / r8.w;
  r10.yzw = r3.xyz * r5.www + r7.www;
  r5.w = r7.w + r5.w;
  r7.w = 1 + -r5.w;
  r5.w = r7.w / r5.w;
  r12.xzw = r5.www * r3.xyz;
  r10.yzw = r12.xzw * r10.yzw + r10.yzw;
  r5.x = t9.SampleLevel(s1_s, r5.xy, 0).x;
  r13.x = t7.Load(r0.xyz).x;
  r0.z = dot(-r9.xyz, r4.xyz);
  r0.z = r0.z + r0.z;
  r14.xyz = r4.xyz * -r0.zzz + -r9.xyz;
  r0.z = dot(-cb3[0].xyz, r14.xyz);
  r12.xzw = -r0.zzz * -cb3[0].xyz + r14.xyz;
  r0.z = cmp(r0.z < cb3[4].z);
  r5.y = dot(r12.xzw, r12.xzw);
  r5.y = rsqrt(r5.y);
  r12.xzw = r12.xzw * r5.yyy;
  r12.xzw = cb3[4].yyy * r12.xzw;
  r12.xzw = cb3[4].zzz * -cb3[0].xyz + r12.xzw;
  r5.y = dot(r12.xzw, r12.xzw);
  r5.y = rsqrt(r5.y);
  r12.xzw = r12.xzw * r5.yyy;
  r12.xzw = r0.zzz ? r12.xzw : r14.xyz;
  r2.x = saturate(dot(r12.xzw, r4.xyz));
  r12.xzw = r7.xyz * r5.zzz + r12.xzw;
  r0.z = dot(r12.xzw, r12.xzw);
  r0.z = rsqrt(r0.z);
  r12.xzw = r12.xzw * r0.zzz;
  r0.z = saturate(dot(r4.xyz, r12.xzw));
  r5.y = saturate(dot(r9.xyz, r12.xzw));
  r2.y = min(1, r10.x);
  r12.xz = -r2.yx * r1.xx + r2.yx;
  r12.xz = r12.xz * r2.yx + r1.xx;
  r12.xz = sqrt(r12.xz);
  r12.xz = r12.xz * r2.xy;
  r5.w = r12.x + r12.z;
  r5.w = 9.99999975e-005 + r5.w;
  r5.w = 0.5 / r5.w;
  r7.w = r0.z * r1.x + -r0.z;
  r0.z = r7.w * r0.z + 1;
  r0.z = r0.z * r0.z;
  r0.z = r1.x / r0.z;
  r1.x = 1 + -r5.y;
  r5.y = r1.x * r1.x;
  r5.y = r5.y * r5.y;
  r7.w = r5.y * r1.x;
  r1.x = -r5.y * r1.x + 1;
  r12.xzw = r3.xyz * r1.xxx + r7.www;
  r0.z = r0.z * r5.w;
  r12.xzw = r0.zzz * r12.xzw;
  r12.xzw = min(float3(2048,2048,2048), r12.xzw);
  r15.xyzw = r2.yzxz * float4(0.96875,0.96875,0.96875,0.96875) + float4(0.015625,0.015625,0.015625,0.015625);
  r0.z = t8.SampleLevel(s4_s, r15.xy, 0).x;
  r1.x = t8.SampleLevel(s4_s, r15.zw, 0).x;
  r5.y = 1 + -r2.z;
  r5.w = -r5.y * 0.383026004 + -0.0761947036;
  r5.w = r5.y * r5.w + 1.04997003;
  r5.y = r5.y * r5.w + 0.409254998;
  r5.y = min(0.999000013, r5.y);
  r5.w = 1 + -r5.y;
  r15.xyz = float3(1,1,1) + -r3.xyz;
  r15.xyz = r15.xyz * float3(0.0476190485,0.0476190485,0.0476190485) + r3.xyz;
  r1.x = r1.x * r0.z;
  r1.x = r1.x * r5.y;
  r1.x = r1.x / r5.w;
  r16.xyz = r15.xyz * r15.xyz;
  r17.xyz = r16.xyz * r1.xxx;
  r15.xyz = -r15.xyz * r5.www + float3(1,1,1);
  r17.xyz = r17.xyz / r15.xyz;
  r12.xzw = r17.xyz + r12.xzw;
  r12.xzw = cb3[4].xxx * r12.xzw;
  r12.xzw = max(float3(0,0,0), r12.xzw);
  r12.xzw = min(float3(1000,1000,1000), r12.xzw);
  r13.yw = float2(0.5,1);
  r17.xyz = t6.SampleBias(s1_s, r13.xy, cb0[88].x).xyz;
  r12.xzw = r12.xzw + r11.xyz;
  r12.xzw = r12.xzw * r2.xxx;
  r12.xzw = cb3[1].xyz * r12.xzw;
  r1.x = 1 + -r13.x;
  r17.xyz = r12.xzw * r17.xyz + -r12.xzw;
  r12.xzw = r1.xxx * r17.xyz + r12.xzw;
  r13.xy = (uint2)r0.xy >> int2(5,5);
  r1.x = mad((int)r13.y, asint(cb2[0].w), (int)r13.x);
  r2.x = (uint)r1.x << 3;
  r7.w = -cb0[65].y * cb2[2].w + abs(r2.w);
  r7.w = (int)r7.w;
  r8.w = (int)r7.w + asint(-cb2[1].y);
  r8.w = (int)r8.w + 1;
  r8.w = max(0, (int)r8.w);
  r8.w = min(1, (int)r8.w);
  r9.w = asint(cb2[1].y) + -1;
  r7.w = min((int)r9.w, (int)r7.w);
  r7.w = (uint)r7.w << 3;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.x, r2.x, l(0), t4.xxxx
r17.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.x = (((uint)r1.x << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.y = (((uint)r1.x << 3) & bitmask.y) | ((uint)2 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.z = (((uint)r1.x << 3) & bitmask.z) | ((uint)3 & ~bitmask.z);
  bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r18.w = (((uint)r1.x << 3) & bitmask.w) | ((uint)4 & ~bitmask.w);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.y, r18.x, l(0), t4.xxxx
r17.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.z, r18.y, l(0), t4.xxxx
r17.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r17.w, r18.z, l(0), t4.xxxx
r17.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.x, r18.w, l(0), t4.xxxx
r18.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.x = (((uint)r1.x << 3) & bitmask.x) | ((uint)5 & ~bitmask.x);
  bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.y = (((uint)r1.x << 3) & bitmask.y) | ((uint)6 & ~bitmask.y);
  bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r19.z = (((uint)r1.x << 3) & bitmask.z) | ((uint)7 & ~bitmask.z);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.y, r19.x, l(0), t4.xxxx
r18.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.z, r19.y, l(0), t4.xxxx
r18.z = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r18.w, r19.z, l(0), t4.xxxx
r18.w = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r1.x = (int)r7.w + asint(cb0[90].y);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r1.x, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r7.w = (int)-r8.w + 1;
  r19.x = (int)r2.x * (int)r7.w;
  r20.xyzw = (int4)r1.xxxx + int4(1,2,3,4);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.x, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.y = (int)r7.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.y, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.z = (int)r7.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.z, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r19.w = (int)r7.w * (int)r2.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r2.x, r20.w, l(0), t4.xxxx
r2.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.x = (int)r7.w * (int)r2.x;
  r21.xyz = (int3)r1.xxx + int3(5,6,7);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.x, r21.x, l(0), t4.xxxx
r1.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.y = (int)r7.w * (int)r1.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.x, r21.y, l(0), t4.xxxx
r1.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.z = (int)r7.w * (int)r1.x;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.x, r21.z, l(0), t4.xxxx
r1.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r20.w = (int)r7.w * (int)r1.x;
  r17.xyzw = (int4)r17.xyzw & (int4)r19.xyzw;
  r18.xyzw = (int4)r18.xyzw & (int4)r20.xyzw;
  x0[0].x = r17.x;
  x0[1].x = r17.y;
  x0[2].x = r17.z;
  x0[3].x = r17.w;
  x0[4].x = r18.x;
  x0[5].x = r18.y;
  x0[6].x = r18.z;
  x0[7].x = r18.w;
  r17.w = 1;
  r18.w = 1;
  r19.z = r2.z;
  r20.xyz = float3(0,0,0);
  r1.x = 1;
  r2.x = 0;
  while (true) {
    r7.w = cmp(7 < (uint)r2.x);
    if (r7.w != 0) break;
    r7.w = x0[r2.x+0].x;
    r8.w = (uint)r2.x << 5;
    r21.xyz = r20.xyz;
    r9.w = r1.x;
    r11.w = r7.w;
    while (true) {
      if (r11.w == 0) break;
      r13.x = firstbitlow((uint)r11.w);
      r13.y = (int)r8.w + (int)r13.x;
      r13.x = 1 << (int)r13.x;
      r13.x = (int)r11.w ^ (int)r13.x;
      bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.x = (((uint)r13.y << 3) & bitmask.x) | ((uint)1 & ~bitmask.x);
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)5 & ~bitmask.y);
      bitmask.z = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.z = (((uint)r13.y << 3) & bitmask.z) | ((uint)6 & ~bitmask.z);
      bitmask.w = ((~(-1 << 29)) << 3) & 0xffffffff;  r22.w = (((uint)r13.y << 3) & bitmask.w) | ((uint)7 & ~bitmask.w);
      r15.w = (uint)cb3[r22.y+6].w;
      r15.w = cmp((int)r15.w == 1);
      if (r15.w != 0) {
        r23.xyz = asuint(cb3[r22.y+6].xyz) >> int3(16,16,16);
        r24.xyz = f16tof32(cb3[r22.y+6].xyz);
        r23.xyz = f16tof32(r23.xzy);
        r25.xyz = asuint(cb3[r22.z+6].xyz) >> int3(16,16,16);
        r26.xyz = f16tof32(cb3[r22.z+6].xyz);
        r25.xyw = f16tof32(r25.xyz);
        r17.xyz = -cb3[r22.x+6].xyz + r6.xyz;
        r27.xz = r24.xy;
        r27.yw = r23.xz;
        r15.w = dot(r17.xyzw, r27.xyzw);
        r23.x = r24.z;
        r23.z = r26.x;
        r23.w = r25.x;
        r16.w = dot(r17.xyzw, r23.xyzw);
        r25.xz = r26.yz;
        r17.x = dot(r17.xyzw, r25.xyzw);
        r15.w = max(abs(r16.w), abs(r15.w));
        r15.w = max(r15.w, abs(r17.x));
        r16.w = cmp(1 < r15.w);
        if (r16.w != 0) {
          r11.w = r13.x;
          continue;
        }
        r16.w = cb3[r22.w+6].x * 0.5 + 0.5;
        r15.w = -r16.w + r15.w;
        r16.w = 1 + -r16.w;
        r15.w = saturate(r15.w / r16.w);
        r15.w = 1 + -r15.w;
        r15.w = r15.w * r15.w;
      } else {
        r15.w = 1;
      }
      r16.w = (uint)r13.y << 3;
      bitmask.x = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.x = (((uint)r13.y << 3) & bitmask.x) | ((uint)2 & ~bitmask.x);
      bitmask.y = ((~(-1 << 29)) << 3) & 0xffffffff;  r17.y = (((uint)r13.y << 3) & bitmask.y) | ((uint)3 & ~bitmask.y);
      r13.y = (uint)cb3[r16.w+6].w;
      r17.z = cmp((uint)r13.y < 2);
      if (r17.z != 0) {
        r17.z = cmp(0.5 >= cb3[r17.y+6].z);
        if (r17.z != 0) {
          r17.z = cb3[r17.x+6].y * 0.5 + 0.5;
          r23.x = -abs(cb3[r17.x+6].x) + r17.z;
          r23.y = cb3[r17.x+6].y + -r23.x;
          r17.z = 1 + -abs(r23.x);
          r17.z = r17.z + -abs(r23.y);
          r17.z = max(5.96046448e-008, r17.z);
          r19.w = cmp(cb3[r17.x+6].x >= 0);
          r23.z = r19.w ? r17.z : -r17.z;
          r17.z = dot(r23.xyz, r23.xyz);
          r17.z = rsqrt(r17.z);
          r23.xyz = r23.xyz * r17.zzz;
          r24.xyz = cb3[r22.x+6].xyz + -r6.xyz;
          r17.z = dot(r24.xyz, r24.xyz);
          r19.w = rsqrt(r17.z);
          r18.xyz = r24.xyz * r19.www;
          r25.xyz = cb3[r17.x+6].zzz * r23.xyz;
          r26.xyz = -r25.xyz * float3(0.5,0.5,0.5) + r24.xyz;
          r27.xyz = r25.xyz * float3(0.5,0.5,0.5) + r24.xyz;
          r20.w = (int)r13.y & 1;
          r21.w = cmp((int)r20.w != 0);
          r22.y = cmp(0 < cb3[r17.x+6].z);
          r21.w = r21.w ? r22.y : 0;
          r13.z = saturate(dot(r4.xyz, r18.xyz));
          r22.y = dot(r26.xyz, r26.xyz);
          r22.y = sqrt(r22.y);
          r23.w = dot(r27.xyz, r27.xyz);
          r23.w = sqrt(r23.w);
          r24.w = dot(r26.xyz, r27.xyz);
          r24.w = r22.y * r23.w + r24.w;
          r24.w = r24.w * 0.5 + 1;
          r28.y = rcp(r24.w);
          r24.w = dot(r4.xyz, r26.xyz);
          r22.y = r24.w / r22.y;
          r24.w = dot(r4.xyz, r27.xyz);
          r23.w = r24.w / r23.w;
          r22.y = r23.w + r22.y;
          r28.x = saturate(0.5 * r22.y);
          r19.xy = r21.ww ? r28.xy : r13.zw;
          r13.z = cmp(cb3[r22.z+6].w < 0);
          r22.y = 1 + r17.z;
          r22.y = 1 / r22.y;
          r23.w = r21.w ? 1.000000 : 0;
          r24.w = -r22.y + r19.y;
          r22.y = r23.w * r24.w + r22.y;
          r23.w = cb3[r22.x+6].w * cb3[r22.x+6].w;
          r17.z = r23.w * r17.z;
          r17.z = -r17.z * r17.z + 1;
          r17.z = max(0, r17.z);
          r17.z = r17.z * r17.z;
          r17.z = r22.y * r17.z;
          r24.xyz = cb3[r22.x+6].www * r24.xyz;
          r22.y = dot(r24.xyz, r24.xyz);
          r22.y = min(1, r22.y);
          r22.y = 1 + -r22.y;
          r22.y = log2(r22.y);
          r22.y = cb3[r22.z+6].w * r22.y;
          r22.y = exp2(r22.y);
          r19.y = r22.y * r19.y;
          r13.z = r13.z ? r17.z : r19.y;
          r17.z = dot(r18.xyz, -r23.xyz);
          r17.z = -cb3[r17.x+6].z + r17.z;
          r17.z = saturate(cb3[r17.x+6].w * r17.z);
          r17.z = r17.z * r17.z;
          r17.z = r17.z * r13.z;
          r13.z = r20.w ? r13.z : r17.z;
          r17.z = cmp(0 < r13.z);
          if (r17.z != 0) {
            r17.z = (int)cb3[r17.y+6].x;
            r23.xyz = -cb3[r22.x+6].xyz + r6.xyz;
            r24.xyz = cmp(abs(r23.yzz) < abs(r23.xxy));
            r19.y = r24.y ? r24.x : 0;
            r24.xyw = cmp(float3(0,0,0) < r23.xyz);
            r22.y = asuint(cb3[r17.x+6].w) >> 24;
            if (8 == 0) r27.x = 0; else if (8+16 < 32) {             r27.x = (uint)cb3[r17.x+6].w << (32-(8 + 16)); r27.x = (uint)r27.x >> (32-8);            } else r27.x = (uint)cb3[r17.x+6].w >> 16;
            if (8 == 0) r27.y = 0; else if (8+8 < 32) {             r27.y = (uint)cb3[r17.x+6].w << (32-(8 + 8)); r27.y = (uint)r27.y >> (32-8);            } else r27.y = (uint)cb3[r17.x+6].w >> 8;
            r22.y = r24.x ? r22.y : r27.x;
            r23.w = 255 & asint(cb3[r17.x+6].w);
            r23.w = r24.y ? r27.y : r23.w;
            if (8 == 0) r24.x = 0; else if (8+8 < 32) {             r24.x = (uint)cb3[r17.y+6].x << (32-(8 + 8)); r24.x = (uint)r24.x >> (32-8);            } else r24.x = (uint)cb3[r17.y+6].x >> 8;
            r24.y = 255 & asint(cb3[r17.y+6].x);
            r24.x = r24.w ? r24.x : r24.y;
            r23.w = r24.z ? r23.w : r24.x;
            r19.y = r19.y ? r22.y : r23.w;
            r22.y = cmp((int)r19.y < 80);
            r19.y = r22.y ? r19.y : -1;
            r17.z = r20.w ? r19.y : r17.z;
            r19.y = cmp((int)r17.z >= 0);
            r20.w = dot(r23.xyz, r23.xyz);
            r20.w = max(1.17549435e-038, r20.w);
            r20.w = rsqrt(r20.w);
            r23.xyz = r23.xyz * r20.www;
            r20.w = dot(r4.xyz, r23.xyz);
            r20.w = max(0, r20.w);
            r20.w = min(0.899999976, r20.w);
            r20.w = 1 + -r20.w;
            r24.xy = cb4[r17.z+256].xy * r20.ww;
            r20.w = 5 * r24.y;
            r23.xyz = -r23.xyz * r24.xxx + r6.xyz;
            r23.xyz = r4.xyz * r20.www + r23.xyz;
            r20.w = (uint)r17.z << 2;
            r24.xyzw = cb4[r20.w+33].xyzw * r23.yyyy;
            r24.xyzw = cb4[r20.w+32].xyzw * r23.xxxx + r24.xyzw;
            r23.xyzw = cb4[r20.w+34].xyzw * r23.zzzz + r24.xyzw;
            r23.xyzw = cb4[r20.w+35].xyzw + r23.xyzw;
            r23.xyz = r23.xyz / r23.www;
            r24.xy = cb4[r17.z+312].zw + -cb4[r17.z+312].xy;
            r24.xy = r23.xy * r24.xy + cb4[r17.z+312].xy;
            r27.xyz = cmp(float3(0,0,0) >= r23.xyz);
            r23.xyw = cmp(r23.xyz >= float3(1,1,1));
            r23.xyw = (int3)r23.xyw | (int3)r27.xyz;
            r20.w = (int)r23.y | (int)r23.x;
            r20.w = (int)r23.w | (int)r20.w;
            r22.y = (int)r23.z & 0x7fffffff;
            r22.y = cmp(0x7f800000 < (uint)r22.y);
            r20.w = (int)r20.w | (int)r22.y;
            r23.xy = r24.xy * cb4[368].zw + float2(0.5,0.5);
            r23.xy = floor(r23.xy);
            r24.xy = r24.xy * cb4[368].zw + -r23.xy;
            r27.xyzw = float4(0.5,1,0.5,1) + r24.xxyy;
            r28.xyzw = r27.xxzz * r27.xxzz;
            r24.zw = float2(0.0799999982,0.0799999982) * r28.yw;
            r27.xz = r28.xz * float2(0.5,0.5) + -r24.xy;
            r28.xy = float2(1,1) + -r24.xy;
            r28.zw = min(float2(0,0), r24.xy);
            r28.zw = -r28.zw * r28.zw + r28.xy;
            r24.xy = max(float2(0,0), r24.xy);
            r24.xy = -r24.xy * r24.xy + r27.yw;
            r28.zw = float2(1,1) + r28.zw;
            r24.xy = float2(1,1) + r24.xy;
            r29.xy = float2(0.159999996,0.159999996) * r27.xz;
            r30.xy = float2(0.159999996,0.159999996) * r28.xy;
            r28.xy = float2(0.159999996,0.159999996) * r28.zw;
            r31.xy = float2(0.159999996,0.159999996) * r24.xy;
            r24.xy = float2(0.159999996,0.159999996) * r27.yw;
            r29.z = r28.x;
            r29.w = r24.x;
            r30.z = r31.x;
            r30.w = r24.z;
            r27.xyzw = r30.zwxz + r29.zwxz;
            r28.z = r29.y;
            r28.w = r24.y;
            r31.z = r30.y;
            r31.w = r24.w;
            r24.xyz = r31.zyw + r28.zyw;
            r28.xyz = r30.xzw / r27.zwy;
            r28.xyz = float3(-2.5,-0.5,1.5) + r28.xyz;
            r29.xyz = r31.zyw / r24.xyz;
            r29.xyz = float3(-2.5,-0.5,1.5) + r29.xyz;
            r28.xyz = cb4[368].xxx * r28.yxz;
            r29.xyz = cb4[368].yyy * r29.xyz;
            r28.w = r29.x;
            r30.xyzw = r23.xyxy * cb4[368].xyxy + r28.ywxw;
            r31.xy = r23.xy * cb4[368].xy + r28.zw;
            r29.w = r28.y;
            r28.yw = r29.yz;
            r32.xyzw = r23.xyxy * cb4[368].xyxy + r28.xyzy;
            r29.xyzw = r23.xyxy * cb4[368].xyxy + r29.wywz;
            r28.xyzw = r23.xyxy * cb4[368].xyxy + r28.xwzw;
            r33.xyzw = r27.zwyz * r24.xxxy;
            r34.xyzw = r27.xyzw * r24.yyzz;
            r22.y = r27.y * r24.z;
            r23.x = t5.SampleCmpLevelZero(s3_s, r30.xy, r23.z).x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r30.zw, r23.z).x;
            r23.y = r33.y * r23.y;
            r23.x = r33.x * r23.x + r23.y;
            r23.y = t5.SampleCmpLevelZero(s3_s, r31.xy, r23.z).x;
            r23.x = r33.z * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r29.xy, r23.z).x;
            r23.x = r33.w * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r32.xy, r23.z).x;
            r23.x = r34.x * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r32.zw, r23.z).x;
            r23.x = r34.y * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r29.zw, r23.z).x;
            r23.x = r34.z * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r28.xy, r23.z).x;
            r23.x = r34.w * r23.y + r23.x;
            r23.y = t5.SampleCmpLevelZero(s3_s, r28.zw, r23.z).x;
            r22.y = r22.y * r23.y + r23.x;
            r22.y = -1 + r22.y;
            r17.z = cb4[r17.z+256].w * r22.y + 1;
            r17.z = r20.w ? 1 : r17.z;
            r17.z = r19.y ? r17.z : 1;
            r19.y = saturate(cb3[r22.w+6].y * r19.w);
            r19.w = saturate(cb3[r17.x+6].z * r19.w);
            r19.w = r19.w * 0.5 + r12.y;
            r19.w = min(1, r19.w);
            r23.w = r12.y / r19.w;
            r19.w = dot(r14.xyz, r25.xyz);
            r24.xyz = r19.www * r14.xyz + -r25.xyz;
            r20.w = dot(r26.xyz, r24.xyz);
            r19.w = r19.w * r19.w;
            r19.w = cb3[r17.x+6].z * cb3[r17.x+6].z + -r19.w;
            r19.w = saturate(r20.w / r19.w);
            r24.xyz = r19.www * r25.xyz + r26.xyz;
            r19.w = dot(r24.xyz, r24.xyz);
            r19.w = rsqrt(r19.w);
            r23.xyz = r24.xyz * r19.www;
            r23.xyzw = r21.wwww ? r23.xyzw : r18.xyzw;
            r18.xyz = r7.xyz * r5.zzz + r23.xyz;
            r19.w = dot(r18.xyz, r18.xyz);
            r19.w = rsqrt(r19.w);
            r18.xyz = r19.www * r18.xyz;
            r19.w = saturate(dot(r4.xyz, r18.xyz));
            r18.x = saturate(dot(r9.xyz, r18.xyz));
            r18.y = cmp(0 < r19.y);
            r18.z = r19.y * r19.y;
            r19.y = r18.x * 3.5999999 + 0.400000006;
            r18.z = r18.z / r19.y;
            r18.z = r2.z * r2.z + r18.z;
            r18.z = min(1, r18.z);
            r18.y = r18.y ? r18.z : r12.y;
            r18.y = r18.y * r18.y;
            r18.z = -r2.y * r18.y + r2.y;
            r18.z = r18.z * r2.y + r18.y;
            r18.z = sqrt(r18.z);
            r19.y = -r19.x * r18.y + r19.x;
            r19.y = r19.y * r19.x + r18.y;
            r19.y = sqrt(r19.y);
            r19.y = r19.y * r2.y;
            r18.z = r19.x * r18.z + r19.y;
            r18.z = 9.99999975e-005 + r18.z;
            r18.z = 0.5 / r18.z;
            r19.y = r19.w * r18.y + -r19.w;
            r19.y = r19.y * r19.w + 1;
            r19.y = r19.y * r19.y;
            r18.y = r18.y / r19.y;
            r18.y = r18.y * r23.w;
            r18.x = 1 + -r18.x;
            r19.y = r18.x * r18.x;
            r19.y = r19.y * r19.y;
            r19.w = r19.y * r18.x;
            r18.x = -r19.y * r18.x + 1;
            r23.xyz = r3.xyz * r18.xxx + r19.www;
            r18.x = r18.y * r18.z;
            r18.xyz = r18.xxx * r23.xyz;
            r18.xyz = min(float3(2048,2048,2048), r18.xyz);
            r19.yw = r19.xz * float2(0.96875,0.96875) + float2(0.015625,0.015625);
            r19.y = t8.SampleLevel(s4_s, r19.yw, 0).x;
            r19.y = r19.y * r0.z;
            r19.y = r19.y * r5.y;
            r19.y = r19.y / r5.w;
            r23.xyz = r19.yyy * r16.xyz;
            r23.xyz = r23.xyz / r15.xyz;
            r18.xyz = r23.xyz + r18.xyz;
            r18.xyz = cb3[r22.w+6].zzz * r18.xyz;
            r18.xyz = max(float3(0,0,0), r18.xyz);
            r18.xyz = min(float3(1000,1000,1000), r18.xyz);
            r23.xyz = cb3[r16.w+6].xyz * r13.zzz;
            r23.xyz = r23.xyz * r17.zzz;
            r23.xyz = r23.xyz * r15.www;
            r18.xyz = r18.xyz + r11.xyz;
            r18.xyz = r18.xyz * r19.xxx;
            r18.xyz = r23.xyz * r18.xyz;
          } else {
            r18.xyz = float3(0,0,0);
          }
        } else {
          r18.xyz = float3(0,0,0);
        }
        r21.xyz = r21.xyz + r18.xyz;
      } else {
        r13.z = cmp(0.5 >= cb3[r17.y+6].z);
        if (r13.z != 0) {
          r13.z = cb3[r17.x+6].y * 0.5 + 0.5;
          r18.x = -abs(cb3[r17.x+6].x) + r13.z;
          r18.y = cb3[r17.x+6].y + -r18.x;
          r13.z = 1 + -abs(r18.x);
          r13.z = r13.z + -abs(r18.y);
          r13.z = max(5.96046448e-008, r13.z);
          r15.w = cmp(cb3[r17.x+6].x >= 0);
          r18.z = r15.w ? r13.z : -r13.z;
          r13.z = dot(r18.xyz, r18.xyz);
          r13.z = rsqrt(r13.z);
          r18.xyz = r18.xyz * r13.zzz;
          r19.xyw = cb3[r22.x+6].xyz + -r6.xyz;
          r13.z = dot(r19.xyw, r19.xyw);
          r15.w = rsqrt(r13.z);
          r23.xyz = r19.xyw * r15.www;
          r24.xyz = cb3[r17.x+6].zzz * r18.xyz;
          r25.xyz = -r24.xyz * float3(0.5,0.5,0.5) + r19.xyw;
          r24.xyz = r24.xyz * float3(0.5,0.5,0.5) + r19.xyw;
          r13.y = (int)r13.y & 1;
          r15.w = cmp((int)r13.y != 0);
          r16.w = cmp(0 < cb3[r17.x+6].z);
          r15.w = r15.w ? r16.w : 0;
          r16.w = dot(r25.xyz, r25.xyz);
          r16.w = sqrt(r16.w);
          r17.z = dot(r24.xyz, r24.xyz);
          r17.z = sqrt(r17.z);
          r20.w = dot(r25.xyz, r24.xyz);
          r16.w = r16.w * r17.z + r20.w;
          r16.w = r16.w * 0.5 + 1;
          r16.w = rcp(r16.w);
          r16.w = r15.w ? r16.w : 1;
          r17.z = cmp(cb3[r22.z+6].w < 0);
          r20.w = 1 + r13.z;
          r20.w = 1 / r20.w;
          r15.w = r15.w ? 1.000000 : 0;
          r21.w = -r20.w + r16.w;
          r15.w = r15.w * r21.w + r20.w;
          r20.w = cb3[r22.x+6].w * cb3[r22.x+6].w;
          r13.z = r20.w * r13.z;
          r13.z = -r13.z * r13.z + 1;
          r13.z = max(0, r13.z);
          r13.z = r13.z * r13.z;
          r13.z = r15.w * r13.z;
          r19.xyw = cb3[r22.x+6].www * r19.xyw;
          r15.w = dot(r19.xyw, r19.xyw);
          r15.w = min(1, r15.w);
          r15.w = 1 + -r15.w;
          r15.w = log2(r15.w);
          r15.w = cb3[r22.z+6].w * r15.w;
          r15.w = exp2(r15.w);
          r15.w = r16.w * r15.w;
          r13.z = r17.z ? r13.z : r15.w;
          r15.w = dot(r23.xyz, -r18.xyz);
          r15.w = -cb3[r17.x+6].z + r15.w;
          r15.w = saturate(cb3[r17.x+6].w * r15.w);
          r15.w = r15.w * r15.w;
          r15.w = r15.w * r13.z;
          r13.z = r13.y ? r13.z : r15.w;
          r13.z = cmp(0 < r13.z);
          if (r13.z != 0) {
            r13.z = (int)cb3[r17.y+6].x;
            r18.xyz = -cb3[r22.x+6].xyz + r6.xyz;
            r19.xyw = cmp(abs(r18.yzz) < abs(r18.xxy));
            r15.w = r19.y ? r19.x : 0;
            r22.xyz = cmp(float3(0,0,0) < r18.xyz);
            r16.w = asuint(cb3[r17.x+6].w) >> 24;
            if (8 == 0) r19.x = 0; else if (8+16 < 32) {             r19.x = (uint)cb3[r17.x+6].w << (32-(8 + 16)); r19.x = (uint)r19.x >> (32-8);            } else r19.x = (uint)cb3[r17.x+6].w >> 16;
            if (8 == 0) r19.y = 0; else if (8+8 < 32) {             r19.y = (uint)cb3[r17.x+6].w << (32-(8 + 8)); r19.y = (uint)r19.y >> (32-8);            } else r19.y = (uint)cb3[r17.x+6].w >> 8;
            r16.w = r22.x ? r16.w : r19.x;
            r17.x = 255 & asint(cb3[r17.x+6].w);
            r17.x = r22.y ? r19.y : r17.x;
            if (8 == 0) r17.z = 0; else if (8+8 < 32) {             r17.z = (uint)cb3[r17.y+6].x << (32-(8 + 8)); r17.z = (uint)r17.z >> (32-8);            } else r17.z = (uint)cb3[r17.y+6].x >> 8;
            r17.y = 255 & asint(cb3[r17.y+6].x);
            r17.y = r22.z ? r17.z : r17.y;
            r17.x = r19.w ? r17.x : r17.y;
            r15.w = r15.w ? r16.w : r17.x;
            r16.w = cmp((int)r15.w < 80);
            r15.w = r16.w ? r15.w : -1;
            r13.y = r13.y ? r15.w : r13.z;
            r13.z = cmp((int)r13.y >= 0);
            r15.w = dot(r18.xyz, r18.xyz);
            r15.w = max(1.17549435e-038, r15.w);
            r15.w = rsqrt(r15.w);
            r17.xyz = r18.xyz * r15.www;
            r15.w = dot(r4.xyz, r17.xyz);
            r15.w = max(0, r15.w);
            r15.w = min(0.899999976, r15.w);
            r15.w = 1 + -r15.w;
            r18.xy = cb4[r13.y+256].xy * r15.ww;
            r15.w = 5 * r18.y;
            r17.xyz = -r17.xyz * r18.xxx + r6.xyz;
            r17.xyz = r4.xyz * r15.www + r17.xyz;
            r15.w = (uint)r13.y << 2;
            r22.xyzw = cb4[r15.w+33].xyzw * r17.yyyy;
            r22.xyzw = cb4[r15.w+32].xyzw * r17.xxxx + r22.xyzw;
            r22.xyzw = cb4[r15.w+34].xyzw * r17.zzzz + r22.xyzw;
            r22.xyzw = cb4[r15.w+35].xyzw + r22.xyzw;
            r17.xyz = r22.xyz / r22.www;
            r18.xy = cb4[r13.y+312].zw + -cb4[r13.y+312].xy;
            r18.xy = r17.xy * r18.xy + cb4[r13.y+312].xy;
            r19.xyw = cmp(float3(0,0,0) >= r17.xyz);
            r22.xyz = cmp(r17.xyz >= float3(1,1,1));
            r19.xyw = (int3)r19.xyw | (int3)r22.xyz;
            r15.w = (int)r19.y | (int)r19.x;
            r15.w = (int)r19.w | (int)r15.w;
            r16.w = (int)r17.z & 0x7fffffff;
            r16.w = cmp(0x7f800000 < (uint)r16.w);
            r15.w = (int)r15.w | (int)r16.w;
            r17.xy = r18.xy * cb4[368].zw + float2(0.5,0.5);
            r17.xy = floor(r17.xy);
            r18.xy = r18.xy * cb4[368].zw + -r17.xy;
            r22.xyzw = float4(0.5,1,0.5,1) + r18.xxyy;
            r23.xyzw = r22.xxzz * r22.xxzz;
            r19.xy = float2(0.0799999982,0.0799999982) * r23.yw;
            r22.xz = r23.xz * float2(0.5,0.5) + -r18.xy;
            r23.xy = float2(1,1) + -r18.xy;
            r23.zw = min(float2(0,0), r18.xy);
            r23.zw = -r23.zw * r23.zw + r23.xy;
            r18.xy = max(float2(0,0), r18.xy);
            r18.xy = -r18.xy * r18.xy + r22.yw;
            r23.zw = float2(1,1) + r23.zw;
            r18.xy = float2(1,1) + r18.xy;
            r24.xy = float2(0.159999996,0.159999996) * r22.xz;
            r25.xy = float2(0.159999996,0.159999996) * r23.xy;
            r23.xy = float2(0.159999996,0.159999996) * r23.zw;
            r26.xy = float2(0.159999996,0.159999996) * r18.xy;
            r18.xy = float2(0.159999996,0.159999996) * r22.yw;
            r24.z = r23.x;
            r24.w = r18.x;
            r25.z = r26.x;
            r25.w = r19.x;
            r22.xyzw = r25.zwxz + r24.zwxz;
            r23.z = r24.y;
            r23.w = r18.y;
            r26.z = r25.y;
            r26.w = r19.y;
            r18.xyz = r26.zyw + r23.zyw;
            r19.xyw = r25.xzw / r22.zwy;
            r19.xyw = float3(-2.5,-0.5,1.5) + r19.xyw;
            r23.xyz = r26.zyw / r18.xyz;
            r23.xyz = float3(-2.5,-0.5,1.5) + r23.xyz;
            r24.xyz = cb4[368].xxx * r19.yxw;
            r23.xyz = cb4[368].yyy * r23.xyz;
            r24.w = r23.x;
            r25.xyzw = r17.xyxy * cb4[368].xyxy + r24.ywxw;
            r19.xy = r17.xy * cb4[368].xy + r24.zw;
            r23.w = r24.y;
            r24.yw = r23.yz;
            r26.xyzw = r17.xyxy * cb4[368].xyxy + r24.xyzy;
            r23.xyzw = r17.xyxy * cb4[368].xyxy + r23.wywz;
            r24.xyzw = r17.xyxy * cb4[368].xyxy + r24.xwzw;
            r27.xyzw = r22.zwyz * r18.xxxy;
            r28.xyzw = r22.xyzw * r18.yyzz;
            r16.w = r22.y * r18.z;
            r17.x = t5.SampleCmpLevelZero(s3_s, r25.xy, r17.z).x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r25.zw, r17.z).x;
            r17.y = r27.y * r17.y;
            r17.x = r27.x * r17.x + r17.y;
            r17.y = t5.SampleCmpLevelZero(s3_s, r19.xy, r17.z).x;
            r17.x = r27.z * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r23.xy, r17.z).x;
            r17.x = r27.w * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r26.xy, r17.z).x;
            r17.x = r28.x * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r26.zw, r17.z).x;
            r17.x = r28.y * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r23.zw, r17.z).x;
            r17.x = r28.z * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r24.xy, r17.z).x;
            r17.x = r28.w * r17.y + r17.x;
            r17.y = t5.SampleCmpLevelZero(s3_s, r24.zw, r17.z).x;
            r16.w = r16.w * r17.y + r17.x;
            r16.w = -1 + r16.w;
            r13.y = cb4[r13.y+256].w * r16.w + 1;
            r13.y = r15.w ? 1 : r13.y;
            r13.y = r13.z ? r13.y : 1;
          } else {
            r13.y = 1;
          }
        } else {
          r13.y = 1;
        }
        r9.w = r13.y * r9.w;
      }
      r11.w = r13.x;
    }
    r20.xyz = r21.xyz;
    r1.x = r9.w;
    r2.x = (int)r2.x + 1;
  }
  r3.xyz = r20.xyz * r1.xxx;
  r3.xyz = r12.xzw * r5.xxx + r3.xyz;
  r2.xy = cmp(cb0[92].xy != float2(0,0));
  if (r2.x != 0) {
    r0.z = t2.SampleBias(s0_s, v1.xy, cb0[88].x).x;
    r0.z = min(r0.z, r1.y);
    r1.x = r10.x + r0.z;
    r2.x = r2.z * -16 + -1;
    r2.x = exp2(r2.x);
    r1.x = log2(abs(r1.x));
    r1.x = r2.x * r1.x;
    r1.x = exp2(r1.x);
    r1.x = -1 + r1.x;
    r1.x = r1.x + r0.z;
    r5.xyz = saturate(r1.xxx);
    r7.xyz = r11.xyz * float3(2.04040003,2.04040003,2.04040003) + float3(-0.332399994,-0.332399994,-0.332399994);
    r12.xyz = r11.xyz * float3(-4.79510021,-4.79510021,-4.79510021) + float3(0.641700029,0.641700029,0.641700029);
    r13.xyz = r11.xyz * float3(2.75519991,2.75519991,2.75519991) + float3(0.690299988,0.690299988,0.690299988);
    r7.xyz = r0.zzz * r7.xyz + r12.xyz;
    r7.xyz = r7.xyz * r0.zzz + r13.xyz;
    r7.xyz = r7.xyz * r0.zzz;
    r7.xyz = max(r7.xyz, r0.zzz);
  } else {
    r5.xyz = r1.yyy;
    r7.xyz = r1.yyy;
  }
  r4.w = 1;
  r12.x = dot(cb0[178].xyzw, r4.xyzw);
  r12.y = dot(cb0[179].xyzw, r4.xyzw);
  r12.z = dot(cb0[180].xyzw, r4.xyzw);
  r13.xyz = r4.xyz * float3(0.25,0.25,0.25) + r6.xyz;
  r0.z = 64 * cb0[177].x;
  r0.z = cmp(r0.z >= -r0.z);
  r1.xy = r0.zz ? float2(64,0.015625) : float2(-64,-0.015625);
  r0.z = cb0[177].x * r1.y;
  r0.z = frac(r0.z);
  r0.z = r1.x * r0.z;
  r0.z = trunc(r0.z);
  r1.xy = r0.zz * float2(2.08299994,4.8670001) + r1.zw;
  r0.z = dot(r1.xy, float2(0.0671105608,0.00583714992));
  r0.z = frac(r0.z);
  r0.z = 52.9829178 * r0.z;
  r0.z = frac(r0.z);
  r0.z = r0.z * 2 + -1;
  r13.xyz = r0.zzz * float3(0.200000003,0.200000003,0.200000003) + r13.xyz;
  r15.xyz = -cb0[175].xyz + r13.xyz;
  r0.z = max(abs(r15.x), abs(r15.y));
  r0.z = max(r0.z, abs(r15.z));
  r1.x = -896 + r0.z;
  r1.x = saturate(0.015625 * r1.x);
  r1.y = cmp(0 < cb0[175].w);
  r2.x = cmp(r1.x < 1);
  r1.y = r1.y ? r2.x : 0;
  if (r1.y != 0) {
    r15.xy = float2(-100,-200) + r0.zz;
    r15.xw = saturate(float2(0.0625,0.0833333358) * r15.yx);
    r16.xy = cmp(r15.wx < float2(1,1));
    r17.xyz = float3(0.001953125,0.00048828125,1);
    r17.w = r15.x;
    r17.xyzw = r16.yyyy ? r17.xyzw : float4(0.00048828125,0.00048828125,2,0);
    r15.xyz = float3(0.00390625,0.001953125,0);
    r15.xyzw = r16.xxxx ? r15.xyzw : r17.xyzw;
    r16.xyz = r15.xxx * r13.xyz;
    r16.xyz = frac(r16.xyz);
    r16.xyzw = t11.SampleLevel(s0_s, r16.xyz, r15.z).xyzw;
    r16.xyzw = r16.xyzw * float4(255,255,255,255) + float4(0.5,0.5,0.5,0.5);
    r16.xyzw = floor(r16.xyzw);
    r0.z = cmp(0 < r16.w);
    if (r0.z != 0) {
      r17.xyz = r13.xyz / r16.www;
      r17.xyz = frac(r17.xyz);
      r17.xyz = r17.xyz * float3(4,4,4) + float3(0.5,0.5,0.5);
      r16.xyz = r16.xyz * float3(5,5,5) + r17.xyz;
      r16.xyz = cb0[176].xyz * r16.xyz;
      r17.xyz = t12.SampleLevel(s1_s, r16.xyz, 0).xyz;
      r16.w = 0.333333343 * r16.z;
      r18.xyz = t13.SampleLevel(s1_s, r16.xyw, 0).xyz;
      r19.xyz = r16.xyz * float3(1,1,0.333333343) + float3(0,0,0.333333343);
      r19.xyz = t13.SampleLevel(s1_s, r19.xyz, 0).xyz;
      r16.xyz = r16.xyz * float3(1,1,0.333333343) + float3(0,0,0.666666687);
      r16.xyz = t13.SampleLevel(s1_s, r16.xyz, 0).xyz;
      r18.xyz = r18.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r18.xyz, r4.xyz);
      r0.z = r17.x * r0.z + r17.x;
      r18.x = max(0, r0.z);
      r19.xyz = r19.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r19.xyz, r4.xyz);
      r0.z = r17.y * r0.z + r17.y;
      r18.y = max(0, r0.z);
      r16.xyz = r16.xyz * float3(4,4,4) + float3(-2,-2,-2);
      r0.z = dot(r16.xyz, r4.xyz);
      r0.z = r17.z * r0.z + r17.z;
      r18.z = max(0, r0.z);
    } else {
      r18.xyz = r12.xyz;
    }
    r0.z = cmp(0 < r15.w);
    if (r0.z != 0) {
      r16.xyz = r15.yyy * r13.xyz;
      r16.xyz = frac(r16.xyz);
      r0.z = 1 + r15.z;
      r16.xyzw = t11.SampleLevel(s0_s, r16.xyz, r0.z).xyzw;
      r16.xyzw = r16.xyzw * float4(255,255,255,255) + float4(0.5,0.5,0.5,0.5);
      r16.xyzw = floor(r16.xyzw);
      r0.z = cmp(0 < r16.w);
      if (r0.z != 0) {
        r13.xyz = r13.xyz / r16.www;
        r13.xyz = frac(r13.xyz);
        r13.xyz = r13.xyz * float3(4,4,4) + float3(0.5,0.5,0.5);
        r13.xyz = r16.xyz * float3(5,5,5) + r13.xyz;
        r13.xyz = cb0[176].xyz * r13.xyz;
        r15.xyz = t12.SampleLevel(s1_s, r13.xyz, 0).xyz;
        r13.w = 0.333333343 * r13.z;
        r16.xyz = t13.SampleLevel(s1_s, r13.xyw, 0).xyz;
        r17.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.333333343);
        r17.xyz = t13.SampleLevel(s1_s, r17.xyz, 0).xyz;
        r13.xyz = r13.xyz * float3(1,1,0.333333343) + float3(0,0,0.666666687);
        r13.xyz = t13.SampleLevel(s1_s, r13.xyz, 0).xyz;
        r16.xyz = r16.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r16.xyz, r4.xyz);
        r0.z = r15.x * r0.z + r15.x;
        r16.x = max(0, r0.z);
        r17.xyz = r17.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r17.xyz, r4.xyz);
        r0.z = r15.y * r0.z + r15.y;
        r16.y = max(0, r0.z);
        r13.xyz = r13.xyz * float3(4,4,4) + float3(-2,-2,-2);
        r0.z = dot(r13.xyz, r4.xyz);
        r0.z = r15.z * r0.z + r15.z;
        r16.z = max(0, r0.z);
        r4.xyz = r16.xyz + -r18.xyz;
        r18.xyz = r15.www * r4.xyz + r18.xyz;
      }
    }
    r4.xyz = -r18.xyz + r12.xyz;
    r12.xyz = r1.xxx * r4.xyz + r18.xyz;
  } else {
    r4.xyz = r12.xyz;
  }
  r4.xyz = r1.yyy ? r12.xyz : r4.xyz;
  r11.xyz = r4.xyz * r11.xyz;
  r11.xyz = cb0[91].xxx * r11.xyz;
  r0.z = max(0.00100000005, r2.z);
  r0.z = log2(r0.z);
  r0.z = -r0.z * 1.20000005 + 1;
  r0.z = 6 + -r0.z;
  r1.xy = (uint2)cb1[0].wx;
  uiDest.xz = (uint2)r0.xy / (uint2)r1.xx;
  r2.xz = uiDest.xz;
  r1.x = mad((int)r2.z, (int)r1.y, (int)r2.x);
  r2.xz = (uint2)cb1[1].xy;
  r1.y = (uint)r2.z;
  r1.y = cb1[2].y / r1.y;
  r1.y = abs(r2.w) + -r1.y;
  r1.y = (int)r1.y;
  r2.z = (int)-r2.x + (int)r1.y;
  r2.xz = (int2)r2.xz + int2(-1,1);
  r2.z = min(1, (uint)r2.z);
  r1.y = min((uint)r2.x, (uint)r1.y);
  r1.xy = (int2)r1.xy + asint(cb0[90].zw);
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.x, r1.x, l(0), t4.xxxx
r1.x = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
// Missing reflection info for shader. No names possible.
// Known bad code for instruction (needs manual fix):
ld_structured_indexable(structured_buffer, stride=4)(mixed,mixed,mixed,mixed) r1.y, r1.y, l(0), t4.xxxx
r1.y = no_StructuredBufferName[no_srcAddressRegister].no_srcByteOffsetName.swiz;
  r1.y = (int)r2.z * (int)r1.y;
  r1.x = (int)r1.y & (int)r1.x;
  r1.y = dot(r4.xyz, float3(0.212672904,0.715152204,0.0721750036));
  r6.w = 1;
  r4.w = 1;
  r12.xyz = float3(0,0,0);
  r2.x = r1.x;
  r2.z = 0;
  r5.w = -1;
  r7.w = 0;
  while (true) {
    if (r2.x == 0) break;
    r8.w = firstbitlow((uint)r2.x);
    r9.w = 1 << (int)r8.w;
    r2.x = (int)r2.x ^ (int)r9.w;
    r8.w = (int)r8.w * 6;
    r13.x = dot(cb1[r8.w+6].xyzw, r6.xyzw);
    r13.y = dot(cb1[r8.w+7].xyzw, r6.xyzw);
    r13.z = dot(cb1[r8.w+8].xyzw, r6.xyzw);
    r15.xyz = r13.xyz / cb1[r8.w+5].xyz;
    r9.w = dot(abs(r15.xyz), abs(r15.xyz));
    r9.w = sqrt(r9.w);
    r10.x = cmp(cb1[r8.w+9].w < 0);
    r11.w = cmp(r9.w < 1);
    r12.w = max(abs(r15.y), abs(r15.z));
    r12.w = max(abs(r15.x), r12.w);
    r12.w = cmp(r12.w < 1);
    r11.w = r10.x ? r11.w : r12.w;
    if (r11.w != 0) {
      r11.w = f16tof32(cb1[r8.w+5].w);
      r12.w = asuint(cb1[r8.w+5].w) >> 16;
      r12.w = f16tof32(r12.w);
      r16.w = abs(r12.w);
      r12.w = cmp(0 < r12.w);
      r9.w = -cb1[r8.w+9].x + r9.w;
      r15.xyz = -cb1[r8.w+9].xyz + abs(r15.xyz);
      r15.xyz = r10.xxx ? r9.www : r15.xyz;
      r17.xyz = -cb1[r8.w+9].xyz + float3(1,1,1);
      r15.xyz = saturate(r15.xyz / r17.xyz);
      r15.xyz = float3(1,1,1) + -r15.xyz;
      r9.w = min(r15.y, r15.z);
      r9.w = min(r15.x, r9.w);
      r9.w = r10.x ? r15.x : r9.w;
      r13.w = cmp(r11.w < r5.w);
      r15.x = 1 + -r7.w;
      r9.w = r13.w ? r15.x : r9.w;
      if (r10.x != 0) {
        r15.x = dot(cb1[r8.w+6].xyz, r14.xyz);
        r15.y = dot(cb1[r8.w+7].xyz, r14.xyz);
        r15.z = dot(cb1[r8.w+8].xyz, r14.xyz);
        r10.x = dot(r15.xyz, r15.xyz);
        r13.w = dot(r13.xyz, r15.xyz);
        r15.w = dot(r13.xyz, r13.xyz);
        r15.w = -cb1[r8.w+5].x * cb1[r8.w+5].x + r15.w;
        r15.w = r15.w * r10.x;
        r15.w = r13.w * r13.w + -r15.w;
        r15.w = sqrt(r15.w);
        r13.w = r15.w + -r13.w;
        r10.x = r13.w / r10.x;
        r15.xyz = r15.xyz * r10.xxx + r13.xyz;
        r17.xyz = cb1[r8.w+7].xyz * r15.yyy;
        r15.xyw = r15.xxx * cb1[r8.w+6].xyz + r17.xyz;
        r15.xyz = r15.zzz * cb1[r8.w+8].xyz + r15.xyw;
        r10.x = dot(r15.xyz, r15.xyz);
        r10.x = rsqrt(r10.x);
        r16.xyz = r15.xyz * r10.xxx;
      } else {
        if (r12.w != 0) {
          r15.x = dot(cb1[r8.w+6].xyz, r14.xyz);
          r15.y = dot(cb1[r8.w+7].xyz, r14.xyz);
          r15.z = dot(cb1[r8.w+8].xyz, r14.xyz);
          r17.xyz = cb1[r8.w+5].xyz + -r13.xyz;
          r17.xyz = r17.xyz / r15.xyz;
          r18.xyz = -cb1[r8.w+5].xyz + -r13.xyz;
          r18.xyz = r18.xyz / r15.xyz;
          r19.xyz = cmp(float3(0,0,0) < r15.xyz);
          r17.xyz = r19.xyz ? r17.xyz : r18.xyz;
          r10.x = min(r17.x, r17.y);
          r10.x = min(r10.x, r17.z);
          r13.xyz = r15.xyz * r10.xxx + r13.xyz;
          r15.xyz = cb1[r8.w+7].xyz * r13.yyy;
          r13.xyw = r13.xxx * cb1[r8.w+6].xyz + r15.xyz;
          r13.xyz = r13.zzz * cb1[r8.w+8].xyz + r13.xyw;
          r10.x = dot(r13.xyz, r13.xyz);
          r10.x = rsqrt(r10.x);
          r16.xyz = r13.xyz * r10.xxx;
        } else {
          r16.xyz = r14.xyz;
        }
      }
      r13.xyz = t3.SampleLevel(s2_s, r16.xyzw, r0.z).xyz;
      r4.xyz = r16.xyz;
      r4.x = dot(cb1[r8.w+4].xyzw, r4.xyzw);
      r4.x = max(0, r4.x);
      r4.x = max(9.99999975e-005, r4.x);
      r4.y = r1.y / r4.x;
      r4.y = min(1, abs(r4.y));
      r4.y = r4.y * 2 + r1.y;
      r4.x = 2 + r4.x;
      r4.x = r4.y / r4.x;
      r13.xyz = r13.xyz * r9.www;
      r13.xyz = abs(cb1[r8.w+9].www) * r13.xyz;
      r4.x = -1 + r4.x;
      r4.x = r4.x * cb0[92].w + 1;
      r4.xyz = r13.xyz * r4.xxx + r12.xyz;
      r8.w = r9.w + r7.w;
      r9.w = 1 + r2.z;
      r5.w = r11.w;
    } else {
      r4.xyz = r12.xyz;
      r9.w = r2.z;
      r8.w = r7.w;
    }
    r10.x = cmp(r8.w >= 1);
    r11.w = cmp(r9.w >= 2);
    r10.x = (int)r10.x | (int)r11.w;
    if (r10.x != 0) {
      r12.xyz = r4.xyz;
      r2.z = r9.w;
      r7.w = r8.w;
      break;
    }
    r12.xyz = r4.xyz;
    r2.z = r9.w;
    r7.w = r8.w;
  }
  r1.x = cmp(r2.z < 2);
  r2.x = cmp(r7.w < 1);
  r1.x = r1.x ? r2.x : 0;
  if (r1.x != 0) {
    r14.w = 0;
    r4.xyz = t3.SampleLevel(s2_s, r14.xyzw, r0.z).xyz;
    r14.w = 1;
    r0.z = dot(cb1[3].xyzw, r14.xyzw);
    r0.z = max(0, r0.z);
    r0.z = max(9.99999975e-005, r0.z);
    r1.x = r1.y / r0.z;
    r1.x = min(1, abs(r1.x));
    r1.x = r1.x * 2 + r1.y;
    r0.z = 2 + r0.z;
    r0.z = r1.x / r0.z;
    r1.x = 1 + -r7.w;
    r4.xyz = r4.xyz * r1.xxx;
    r0.z = -1 + r0.z;
    r0.z = r0.z * cb0[92].w + 1;
    r12.xyz = r4.xyz * r0.zzz + r12.xyz;
    r7.w = 1;
  }
  r0.z = max(1, r7.w);
  r4.xyz = r12.xyz / r0.zzz;
  r4.xyz = cb0[92].zzz * r4.xyz;
  r4.xyz = cb0[91].yyy * r4.xyz;
  if (r2.y != 0) {
    r12.xyzw = t1.SampleBias(s1_s, v1.xy, cb0[88].x).xyzw;
    r0.z = 1 + -r12.w;
    r2.xyz = r4.xyz * r0.zzz;
    r4.xyz = r12.xyz * r12.www + r2.xyz;
  }
  r2.xyz = r4.xyz * r10.yzw;
  r2.xyz = r2.xyz * r5.xyz;
  r2.xyz = r11.xyz * r7.xyz + r2.xyz;
  r2.xyz = r3.xyz + r2.xyz;
  r2.xyz = max(float3(0,0,0), r2.xyz);
  r2.xyz = min(float3(255,255,255), r2.xyz);
  r0.z = sqrt(r3.w);
  r0.z = r0.z * cb0[136].w + -cb0[134].w;
  r0.z = max(0, r0.z);
  r1.x = cb0[135].w + -cb0[133].w;
  r1.y = r6.y * 0.00100000005 + -cb0[133].w;
  r3.x = dot(-r9.xyz, cb0[136].xyz);
  r3.yzw = cb0[134].xyz + cb0[132].xyz;
  r4.xyz = cb0[133].xyz + r3.yzw;
  r1.x = r1.x + -r1.y;
  r1.x = r1.x / cb0[131].w;
  r1.x = max(0.00999999978, r1.x);
  r4.w = -1.44269502 * r1.x;
  r4.w = exp2(r4.w);
  r4.w = 1 + -r4.w;
  r1.x = r4.w / r1.x;
  r1.y = -r1.y / cb0[131].w;
  r1.y = 1.44269502 * r1.y;
  r1.y = exp2(r1.y);
  r1.x = r1.x * r1.y;
  r0.z = r1.x * -r0.z;
  r5.xyz = r0.zzz * r4.xyz;
  r5.xyz = float3(1.44269502,1.44269502,1.44269502) * r5.xyz;
  r5.xyz = exp2(r5.xyz);
  r0.z = r3.x * r3.x + 1;
  r0.z = 0.0596831031 * r0.z;
  r1.x = cb0[132].w * cb0[132].w + 1;
  r1.y = cb0[132].w + cb0[132].w;
  r1.x = -r1.y * r3.x + r1.x;
  r1.y = -cb0[132].w * cb0[132].w + 1;
  r3.x = 12.566371 * r1.x;
  r1.x = sqrt(r1.x);
  r1.x = r3.x * r1.x;
  r1.x = r1.y / r1.x;
  r7.xyz = cb0[132].xyz * r1.xxx;
  r7.xyz = cb0[134].xyz * r0.zzz + r7.xyz;
  r3.xyz = cb0[135].xyz * r3.yzw;
  r3.xyz = cb0[131].xyz * r7.xyz + r3.xyz;
  r3.xyz = r3.xyz / r4.xyz;
  r3.xyz = max(float3(0,0,0), r3.xyz);
  r3.xyz = min(float3(255,255,255), r3.xyz);
  r4.xyz = float3(1,1,1) + -r5.xyz;
  r3.xyz = r4.xyz * r3.xyz;
  r2.xyz = r2.xyz * r5.xyz + r3.xyz;
  r0.z = cmp(0 < cb0[141].z);
  if (r0.z != 0) {
    r0.z = abs(r2.w) * cb0[142].x + cb0[142].y;
    r0.z = log2(r0.z);
    r0.z = cb0[142].z * r0.z;
    r3.z = r0.z / cb0[141].z;
    r0.w = asint(cb0[88].w) & 7;
    r0.xyz = mad((int3)r0.xyw, int3(0,0,0), int3(0,0,0));
    r0.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r0.y = mad((int)r0.z, (int)r0.x, (int)r0.y);
    r0.z = mad((int)r0.x, (int)r0.y, (int)r0.z);
    r1.x = mad((int)r0.y, (int)r0.z, (int)r0.x);
    r1.y = mad((int)r0.z, (int)r1.x, (int)r0.y);
    r0.xy = (uint2)r1.xy >> int2(16,16);
    r0.xy = (uint2)r0.xy;
    r0.xy = r0.xy * float2(3.05180438e-005,3.05180438e-005) + float2(-1,-1);
    r0.xy = cb0[145].ww * r0.xy + r1.zw;
    r3.xy = cb0[143].xy * r0.xy;
    r0.x = dot(-r9.xyz, -r8.xyz);
    r0.y = cmp(5.96046448e-008 < r0.x);
    r0.x = rcp(r0.x);
    r0.x = r0.y ? r0.x : 0;
    r0.x = cb0[141].w * r0.x;
    r0.yzw = -cb0[32].xyz + r6.xyz;
    r0.y = dot(r0.yzw, r0.yzw);
    r0.w = max(9.99999994e-009, r0.y);
    r0.w = rsqrt(r0.w);
    r1.xy = r0.yx * r0.ww;
    r1.z = r1.y * r0.z + cb0[32].y;
    r0.z = -r1.y * r0.z + r0.z;
    r0.x = -r0.x * r0.w + 1;
    r0.x = r0.x * r1.x;
    r1.x = -cb0[137].x + r1.z;
    r1.x = cb0[137].z * r1.x;
    r1.x = max(-127, r1.x);
    r1.x = exp2(-r1.x);
    r1.x = cb0[137].y * r1.x;
    r1.y = cb0[137].z * r0.z;
    r1.y = max(-127, r1.y);
    r1.w = exp2(-r1.y);
    r1.w = 1 + -r1.w;
    r1.w = r1.w / r1.y;
    r3.w = -r1.y * 0.240226507 + 0.693147182;
    r1.y = cmp(5.96046448e-008 < abs(r1.y));
    r1.y = r1.y ? r1.w : r3.w;
    r1.x = r1.x * r1.y;
    r1.y = cmp(0 < cb0[140].y);
    r1.z = -cb0[140].z + r1.z;
    r1.z = cb0[140].x * r1.z;
    r1.z = max(-127, r1.z);
    r1.z = exp2(-r1.z);
    r1.z = cb0[140].y * r1.z;
    r0.z = cb0[140].x * r0.z;
    r0.z = max(-127, r0.z);
    r1.w = exp2(-r0.z);
    r1.w = 1 + -r1.w;
    r1.w = r1.w / r0.z;
    r3.w = -r0.z * 0.240226507 + 0.693147182;
    r0.z = cmp(5.96046448e-008 < abs(r0.z));
    r0.z = r0.z ? r1.w : r3.w;
    r0.z = r1.z * r0.z + r1.x;
    r0.z = r1.y ? r0.z : r1.x;
    r0.x = r0.z * r0.x;
    r0.x = exp2(-r0.x);
    r0.x = min(1, r0.x);
    r0.x = max(cb0[139].w, r0.x);
    r0.z = -r0.y * r0.w + cb0[138].x;
    r0.y = r0.y * r0.w + -cb0[138].z;
    r0.yz = saturate(cb0[138].wy * r0.yz);
    r0.x = r0.x + r0.z;
    r0.x = r0.x + r0.y;
    r0.x = min(1, r0.x);
    r0.y = 1 + -r0.x;
    r0.yzw = cb0[139].xyz * r0.yyy;
    r1.xyzw = t10.SampleLevel(s1_s, r3.xyz, 0).xyzw;
    r2.w = -cb0[144].z + abs(r2.w);
    r2.w = saturate(1000000 * r2.w);
    r1.xyzw = float4(-0,-0,-0,-1) + r1.xyzw;
    r1.xyzw = r2.wwww * r1.xyzw + float4(0,0,0,1);
    r0.yzw = r0.yzw * r1.www + r1.xyz;
    r1.w = r1.w * r0.x;
  } else {
    r3.xyz = -cb0[32].xyz + r6.xyz;
    r0.x = dot(r3.xyz, r3.xyz);
    r2.w = max(9.99999994e-009, r0.x);
    r2.w = rsqrt(r2.w);
    r3.x = r2.w * r0.x;
    r3.z = -cb0[137].x + cb0[32].y;
    r3.z = cb0[137].z * r3.z;
    r3.z = max(-127, r3.z);
    r3.z = exp2(-r3.z);
    r3.zw = cb0[137].yz * r3.zy;
    r3.w = max(-127, r3.w);
    r4.x = exp2(-r3.w);
    r4.x = 1 + -r4.x;
    r4.x = r4.x / r3.w;
    r4.y = -r3.w * 0.240226507 + 0.693147182;
    r3.w = cmp(5.96046448e-008 < abs(r3.w));
    r3.w = r3.w ? r4.x : r4.y;
    r3.z = r3.z * r3.w;
    r3.w = cmp(0 < cb0[140].y);
    r4.x = -cb0[140].z + cb0[32].y;
    r4.x = cb0[140].x * r4.x;
    r4.x = max(-127, r4.x);
    r4.x = exp2(-r4.x);
    r4.x = cb0[140].y * r4.x;
    r3.y = cb0[140].x * r3.y;
    r3.y = max(-127, r3.y);
    r4.y = exp2(-r3.y);
    r4.y = 1 + -r4.y;
    r4.y = r4.y / r3.y;
    r4.z = -r3.y * 0.240226507 + 0.693147182;
    r3.y = cmp(5.96046448e-008 < abs(r3.y));
    r3.y = r3.y ? r4.y : r4.z;
    r3.y = r4.x * r3.y + r3.z;
    r3.y = r3.w ? r3.y : r3.z;
    r3.x = r3.y * r3.x;
    r3.x = exp2(-r3.x);
    r3.x = min(1, r3.x);
    r3.x = max(cb0[139].w, r3.x);
    r3.y = -r0.x * r2.w + cb0[138].x;
    r3.y = saturate(cb0[138].y * r3.y);
    r0.x = r0.x * r2.w + -cb0[138].z;
    r0.x = saturate(cb0[138].w * r0.x);
    r2.w = r3.x + r3.y;
    r0.x = r2.w + r0.x;
    r1.w = min(1, r0.x);
    r0.x = 1 + -r1.w;
    r0.yzw = cb0[139].xyz * r0.xxx;
  }
  r1.xyz = r2.xyz * r1.www + r0.yzw;
  o0.xyzw = r1.xyzw;
  o1.xyzw = float4(0,0,0,0);
  return;
}