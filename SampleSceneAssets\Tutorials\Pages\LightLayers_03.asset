%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: LightLayers_03
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 87f93a0fcd40e6246801c025db6d5df8, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Set up Light Layers
      Text:
        m_Untranslated: 'Let''s see how you can assign Light Layers to objects and
          Lights.


          1. In the <i>Hierarchy,</i> select <b>Props</b> > <b>Room3</b>
          > <b>CeilingLamp_01</b> > <b>CeilingLamp_Lamp_01</b> > <b>CeilingLamp_Glass_01_LOD0.</b>


          2.
          In its <i>Inspector,</i> notice that the <b>Rendering Layer Mask</b> parameter
          is set to <b>LampsOnly.</b>


          3. Select <b>Props</b> > <b>Room3</b>
          > <b>CeilingLamp_01</b> > <b>Light_Spot_1200lm.</b>


          Notice
          that the <b>Light Layer</b> is set to <b>Mixed...</b> and once the dropdown
          is expanded, see that the <b>Lamp Only</b> layer is disabled. If the property
          is not visible, enable Show Additional Properties using the 3 dots in the
          General section.


          Because the spotlights in the light fixture
          are unable to affect the <b>Lamp Only</b> layer, the pieces of glass placed
          on the <b>Lamps Only</b> layer cannot receive lighting nor shadowing from
          said spotlights. Thus, no unwanted shadows are produced.



          <b>Expert
          Tips</b>


          Obviously, Light layers are not physically correct,
          because light cannot be blocked this way in real-life. However, in practice,
          this feature is extremely useful to avoid unwanted illumination, shadowing
          and light leaking in more difficult lighting scenarios.


          Light
          layers can be used to fix indoor and outdoor light leaking, and selectively
          light a character in a cutscene without affecting the rest of the environment
          or other characters.'
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 0.3600989
    m_Pivot: {x: 62.922337, y: 4.6784563, z: -0.20594485}
    m_Rotation: {x: -0.023196526, y: 0.70862633, z: 0.023325425, w: 0.70481795}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
