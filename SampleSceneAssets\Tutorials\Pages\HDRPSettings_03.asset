%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff771ccdf4150419d9ff4d342b069aae, type: 3}
  m_Name: HDRPSettings_03
  m_EditorClassIdentifier: 
  m_Paragraphs:
    m_Items:
    - m_Type: 6
      Title:
        m_Untranslated: 
      Text:
        m_Untranslated: 
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 2800000, guid: 0af20b5291f94d54cb2690f236082cc8, type: 3}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
    - m_Type: 0
      Title:
        m_Untranslated: Quality Levels
      Text:
        m_Untranslated: "It is good practice to create several HDRP assets with different
          settings for specific performance targets and platforms, and assigned them
          to dedicated <b>Quality Levels</b>. \n\n1. Go to <b>Edit</b> > <b>Project
          Settings</b> > <b>Quality.</b>\n\nSee that this project offers 5 quality
          levels: <b>Low</b>, <b>Medium</b> and <b>High,</b> as well as 2 quality
          levels with Ray Tracing enabled called <b>Ray Tracing</b> and <b>Ray Tracing
          (Realtime GI).</b>\n\n2. Switch to <b>Low Quality</b> and notice that the
          Fog will disappear. \n\nThis occurs because in the <b>Low</b> quality level,
          the Volumetric Fog is disabled. You can also locate the <b>1HDRPLowQuality</b>
          asset in the <i>Project</i> Window and verify that indeed <b>Lighting</b>
          > <b>Volumetrics</b> > <b>Volumetric Fog</b> is turned off. \n\n3. Change
          back to <b>Medium Quality.</b>\n\n\n<b>Important</b>\n\nIf you have a Ray
          Tracing capable GPU, you can try the <b>Ray Tracing</b> and <b>Ray Tracing
          (Realtime GI)</b> quality levels. Depending on your GPU's performance,
          the framerate might be heavily impacted. "
      m_Tutorial: {fileID: 0}
      m_Image: {fileID: 0}
      m_Video: {fileID: 0}
      m_CriteriaCompletion: 0
      m_Criteria:
        m_Items: []
      m_MaskingSettings:
        m_MaskingEnabled: 0
        m_UnmaskedViews: []
      m_Summary: 
      m_Description: 
      m_InstructionBoxTitle: 
      m_InstructionText: 
      m_TutorialButtonText: 
  m_CameraSettings:
    m_CameraMode: 1
    m_FocusMode: 0
    m_Orthographic: 0
    m_Size: 9.263008
    m_Pivot: {x: 42.387405, y: 0.6678132, z: 5.398338}
    m_Rotation: {x: -0.04070032, y: -0.6076249, z: 0.031211035, w: -0.79263395}
    m_FrameObject:
      m_SceneGuid: 
      m_GameObjectGuid: 
      m_SerializedComponentType:
        m_TypeName: 
      m_ComponentIndex: 0
      m_AssetObject: {fileID: 0}
      m_Prefab: {fileID: 0}
    m_Enabled: 1
  NextButton:
    m_Untranslated: Next
  DoneButton:
    m_Untranslated: Done
  m_CompletedSound: {fileID: 0}
  m_AutoAdvance: 0
  Showing:
    m_PersistentCalls:
      m_Calls: []
  Shown:
    m_PersistentCalls:
      m_Calls: []
  Staying:
    m_PersistentCalls:
      m_Calls: []
  CriteriaValidated:
    m_PersistentCalls:
      m_Calls: []
  MaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  NonMaskingSettingsChanged:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforePageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnAfterPageShown:
    m_PersistentCalls:
      m_Calls: []
  m_OnTutorialPageStay:
    m_PersistentCalls:
      m_Calls: []
  m_OnBeforeTutorialQuit:
    m_PersistentCalls:
      m_Calls: []
  m_NextButton: 
  m_DoneButton: 
